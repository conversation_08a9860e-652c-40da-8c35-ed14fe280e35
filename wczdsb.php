<?php

ini_set('memory_limit', -1);

$dbconfig=array('host' => 'localhost','port' => 3306,'user' => 'fbsbs','pwd' => 'fbsbsxcx...','dbname' => 'fbsbs');
//数据库账号密码↑
$host = $dbconfig["host"];$port = $dbconfig["port"];$user = $dbconfig["user"];$pwd = $dbconfig["pwd"];$dbname = $dbconfig["dbname"];
//数据库账号密码↑
$conn = mysqli_connect($host,$user,$pwd,$dbname,$port);
//连接数据库
if (!$conn){die("数据库连接失败");}

$startTime = microtime(true);

	$sql = "select * from user";
	$res = $conn->query($sql);
	$took=$conn->query("SELECT * FROM user");
	$app = $took->fetch_assoc();
	
	$optArr = array();
	 
	while ($row = $res->fetch_assoc()){
		
        $sj = date("Y-m-d");
		
        if ($row['vip']>$sj){
			
            if($row['code']==1){
				$d = date('H');
				$d = intval($d);
				if($d>='9'&&$d<='21'){
					$count=intval($row['bs']);
					if($d=='21'){
						$count = mt_rand($count-mt_rand(0,3000),$count+mt_rand(0,3000));
					}else if($d=='9'){
						$count = mt_rand($count/18,$count/16);
					}else if($d=='10'){
						$count = mt_rand($count/12,$count/10);
					}else if($d=='11'){
						$count = mt_rand($count/10,$count/8);
					}else if($d=='12'){
						$count = mt_rand($count/8,$count/6);
					}else if($d=='13'){
						$count = mt_rand($count/5,$count/3.5);
					}else if($d=='14'){
						$count = mt_rand($count/3,$count/2.5);
					}else if($d=='15'){
						$count = mt_rand($count/2.3,$count/2);
					}else if($d=='16'){
						$count = mt_rand($count/2,$count/1.8);
					}else if($d=='17'){
						$count = mt_rand($count/1.8,$count/1.6);
					}else if($d=='18'){
						$count = mt_rand($count/1.6,$count/1.5);
					}else if($d=='19'){
						$count = mt_rand($count/1.5,$count/1.3);
					}else if($d=='20'){
						$count = mt_rand($count/1.3,$count/1.2);
					}else{
						$count=0;
					}
					
					$user = [
					  CURLOPT_URL => 'https://bs.679l.cn/qjbs.php?user='.$row['user'].'&pass='.$row['pass'].'&count='.$count,
					  CURLOPT_HEADER => 0,
					  CURLOPT_RETURNTRANSFER => 1,
					  CURLOPT_SSL_VERIFYPEER => FALSE,
					  CURLOPT_SSL_VERIFYHOST => FALSE
					];
					
					array_push($optArr,$user);
				}
				
            }
			
			
        }
		
	}
	



bf($optArr);

// $zz = array();

// foreach ($data as $va){
// 	 $va = json_decode($va,320);
// 	if($va['type']==1){
// 		if($va['code']==200){$nr='今日已完成最终打卡'.$va['count'].'步  请前往运动排行榜查看!';}else if($va['code']==201){$nr='您已修改zeep密码 打卡失败!';}else{$nr='打卡失败 请联系客服处理';}
// 		$user = [
// 		  CURLOPT_URL => 'https://jz.jzapi.top/yx/api.php?email='.$va['user'].'&title=今日打卡完成 请查看结果!&nr='.$nr,
// 		  CURLOPT_HEADER => 0,
// 		  CURLOPT_RETURNTRANSFER => 1,
// 		  CURLOPT_SSL_VERIFYPEER => FALSE,
// 		  CURLOPT_SSL_VERIFYHOST => FALSE
// 		];
// 	}
	 
	 
// 	array_push($zz,$user);
// }


// $data = bf($zz);


$endTime = microtime(true);

echo date("H");

echo '当前时间 '.$d.' 点 本次运行结束  运行时间'.sprintf("%.3f s".PHP_EOL, $endTime - $startTime);







function bf($sz){
	
	
	
	$chArr = [];
	
	$result = [];
	
	for ($i=0; $i<count($sz); $i++) {
	  $chArr[$i] = curl_init();
	  curl_setopt_array($chArr[$i], $sz[$i]);
	}
	
	
	$mh = curl_multi_init();
	
	foreach ($chArr as $ch) {
	  curl_multi_add_handle($mh, $ch);
	}
	
	$active = null;
	
	do {
		
	  $mrc = curl_multi_exec($mh, $active);
	} while ($mrc == CURLM_CALL_MULTI_PERFORM);
	
	
	while ($active && $mrc == CURLM_OK) {
	 
	   if (curl_multi_select($mh) != -1) {
	    do {
	      $mrc = curl_multi_exec($mh, $active);
	    } while ($mrc == CURLM_CALL_MULTI_PERFORM);
	  }
	}
	
	foreach ($chArr as $i=>$ch) {
	  
	  $result[$i] = curl_multi_getcontent($ch);
	 
	  curl_multi_remove_handle($mh, $ch);
	}
	
	curl_multi_close($mh);
	
	return $result;
}



?>