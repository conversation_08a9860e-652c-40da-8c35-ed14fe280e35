<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>使用方法</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    body {
      font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
      background-color: #f4f6f9;
      padding: 15px;
      line-height: 1.6;
      color: #333;
    }
    .card {
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      padding: 20px;
      max-width: 600px;
      margin: 0 auto;
    }
    .card-title {
      display: inline-block;
      background: #4d7cff;
      color: #fff;
      font-weight: bold;
      font-size: 16px;
      padding: 8px 18px;
      border-radius: 8px;
      margin-bottom: 20px;
    }
    .card-content {
      font-size: 15px;
    }
    .card-content ul {
      padding-left: 22px;
      margin-bottom: 15px;
    }
    .card-content li {
      margin-bottom: 12px;
    }
    .card-content p {
      margin: 12px 0;
      color: #555;
    }
    .qrcode-container {
      text-align: center;
      margin: 25px 0 15px;
    }
    .qrcode-img {
      max-width: 100%;
      height: auto;
      border: 1px solid #eee;
      border-radius: 10px;
      padding: 12px;
      background: #fff;
      box-shadow: 0 3px 8px rgba(0,0,0,0.06);
      max-height: 280px;
    }
    .contact-info {
      text-align: center;
      font-weight: bold;
      font-size: 16px;
      margin: 15px 0 10px;
      color: #333;
    }

    /* 手机端特定样式 */
    @media (max-width: 480px) {
      body {
        padding: 10px;
      }
      .card {
        padding: 16px;
        border-radius: 10px;
      }
      .card-title {
        font-size: 15px;
        padding: 7px 15px;
        margin-bottom: 16px;
      }
      .card-content {
        font-size: 14px;
      }
      .card-content ul {
        padding-left: 20px;
      }
      .card-content li {
        margin-bottom: 10px;
      }
      .qrcode-img {
        max-height: 220px;
        padding: 8px;
      }
      .contact-info {
        font-size: 15px;
      }
    }

    /* 超小屏幕优化 */
    @media (max-width: 360px) {
      .card-content {
        font-size: 13.5px;
      }
      .card-title {
        font-size: 14px;
      }
    }
  </style>
</head>
<body>
  <div class="card">
    <div class="card-title">使用方法</div>
    <div class="card-content">
      <ul>
        <li>在应用商店中下载【Zepp Life】APP，打开软件并选择没有账号立即注册，一定要新注册，不能用小米手机的账号。</li>
        <li>进入"Zepp Life" APP，依次点击：我的->第三方接入->绑定你想同步数据的平台。</li>
        <li>回到页面登录后进行修改步数。</li>
      </ul>
      <p>注：修改步数有时会存在延时，提交后等待十分钟左右再查看同步情况。</p>
      <p>若微信未同步请取消"华米科技"公众号关注，重新到zepp life app中绑定微信。</p>
      
      <div class="contact-info">客服微信：z12150301</div>
      <div class="qrcode-container">
        <img class="qrcode-img" src="https://bs.679l.cn/8.jpg" alt="客服微信二维码">
      </div>
    </div>
  </div>
</body>
</html>