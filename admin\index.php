<?php
session_start();
require_once '../config/config.php';
require_once 'auth.php';

// 验证登录状态
checkAdminLogin();

$admin = getCurrentAdmin();

// 获取统计数据
try {
    $db = getDB();
    
    // 用户总数
    $stmt = $db->query("SELECT COUNT(*) as total FROM user");
    $userCount = $stmt->fetch()['total'];
    
    // 小程序用户总数
    $stmt = $db->query("SELECT COUNT(*) as total FROM xcxuser");
    $xcxUserCount = $stmt->fetch()['total'];
    
    // 管理员总数
    $stmt = $db->query("SELECT COUNT(*) as total FROM admin_users");
    $adminCount = $stmt->fetch()['total'];
    
    // 系统配置数量
    $stmt = $db->query("SELECT COUNT(*) as total FROM syskey");
    $syskeyCount = $stmt->fetch()['total'];
    
} catch (Exception $e) {
    $userCount = $xcxUserCount = $adminCount = $syskeyCount = 0;
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>运动步数管理系统</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://unpkg.com/layui@2.8.18/dist/css/layui.css" rel="stylesheet">
</head>
<body class="layui-layout-body">
<div class="layui-layout layui-layout-admin">
    <!-- 头部区域 -->
    <div class="layui-header">
        <div class="layui-logo layui-hide-xs layui-bg-black">运动步数管理系统</div>
        <ul class="layui-nav layui-layout-right">
            <li class="layui-nav-item layui-hide layui-show-md-inline-block">
                <a href="javascript:;">
                    <img src="//unpkg.com/outeres@0.0.10/img/layui/icon-v2.png" class="layui-nav-img">
                    <?php echo htmlspecialchars($admin['username']); ?>
                </a>
                <dl class="layui-nav-child">
                    <dd><a href="javascript:;" data-url="profile.php">基本资料</a></dd>
                    <dd><a href="javascript:;" data-url="change_password.php">修改密码</a></dd>
                </dl>
            </li>
            <li class="layui-nav-item" lay-header-event="menuRight" lay-unselect>
                <a href="logout.php">
                    <i class="layui-icon layui-icon-logout"></i> 退出
                </a>
            </li>
        </ul>
    </div>
    
    <!-- 侧边菜单 -->
    <div class="layui-side layui-bg-black">
        <div class="layui-side-scroll">
            <ul class="layui-nav layui-nav-tree" lay-filter="test">
                <li class="layui-nav-item layui-nav-itemed">
                    <a class="" href="javascript:;">系统概览</a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="dashboard.php">数据统计</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;">用户管理</a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="users.php">普通用户</a></dd>
                        <dd><a href="javascript:;" data-url="xcx_users.php">小程序用户</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;">系统管理</a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:;" data-url="admin_users.php">管理员管理</a></dd>
                        <dd><a href="javascript:;" data-url="system_config.php">系统配置</a></dd>
                    </dl>
                </li>
            </ul>
        </div>
    </div>
    
    <!-- 内容主体区域 -->
    <div class="layui-body">
        <div style="padding: 15px;" id="main-content">
            <!-- 统计卡片 -->
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header">用户总数</div>
                        <div class="layui-card-body">
                            <div style="font-size: 30px; color: #1E9FFF; text-align: center;">
                                <?php echo $userCount; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header">小程序用户</div>
                        <div class="layui-card-body">
                            <div style="font-size: 30px; color: #5FB878; text-align: center;">
                                <?php echo $xcxUserCount; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header">管理员数量</div>
                        <div class="layui-card-body">
                            <div style="font-size: 30px; color: #FF5722; text-align: center;">
                                <?php echo $adminCount; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-header">系统配置</div>
                        <div class="layui-card-body">
                            <div style="font-size: 30px; color: #FFB800; text-align: center;">
                                <?php echo $syskeyCount; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 快捷操作 -->
            <div class="layui-card" style="margin-top: 15px;">
                <div class="layui-card-header">快捷操作</div>
                <div class="layui-card-body">
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md3">
                            <button class="layui-btn layui-btn-fluid" data-url="users.php">
                                <i class="layui-icon layui-icon-user"></i> 用户管理
                            </button>
                        </div>
                        <div class="layui-col-md3">
                            <button class="layui-btn layui-btn-fluid layui-btn-normal" data-url="xcx_users.php">
                                <i class="layui-icon layui-icon-cellphone"></i> 小程序用户
                            </button>
                        </div>
                        <div class="layui-col-md3">
                            <button class="layui-btn layui-btn-fluid layui-btn-warm" data-url="system_config.php">
                                <i class="layui-icon layui-icon-set"></i> 系统配置
                            </button>
                        </div>
                        <div class="layui-col-md3">
                            <button class="layui-btn layui-btn-fluid layui-btn-danger" data-url="admin_users.php">
                                <i class="layui-icon layui-icon-username"></i> 管理员管理
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部固定区域 -->
    <div class="layui-footer">
        © 2024 运动步数管理系统 - Powered by Layui
    </div>
</div>

<script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
<script>
layui.use(['element', 'layer'], function(){
    var element = layui.element;
    var layer = layui.layer;
    
    // 监听导航点击
    element.on('nav(test)', function(elem){
        console.log(elem);
    });
    
    // 监听菜单点击
    document.addEventListener('click', function(e) {
        var target = e.target;
        
        // 查找最近的带有data-url属性的元素
        while (target && !target.getAttribute('data-url')) {
            target = target.parentElement;
        }
        
        if (target && target.getAttribute('data-url')) {
            var url = target.getAttribute('data-url');
            var title = target.textContent.trim();
            
            // 使用iframe加载页面
            var content = '<iframe src="' + url + '" width="100%" height="600" frameborder="0"></iframe>';
            layer.open({
                type: 1,
                title: title,
                area: ['90%', '80%'],
                content: content
            });
        }
    });
});
</script>
</body>
</html>
