<?php
/**
 * 易支付工具类
 */
class EpayHelper {
    private $config;
    
    public function __construct() {
        $this->config = include 'epay_config.php';
    }
    
    /**
     * 生成签名
     * @param array $params 需要签名的参数数组
     * @return string 签名结果
     */
    public function getSign($params) {
        // 1. 过滤空值和签名参数
        $params = array_filter($params, function($value, $key) {
            return $value !== '' && $value !== null && $key != 'sign' && $key != 'sign_type';
        }, ARRAY_FILTER_USE_BOTH);
        
        // 2. 按照参数名ASCII码从小到大排序
        ksort($params);
        
        // 3. 拼接成URL键值对
        $stringA = '';
        foreach ($params as $k => $v) {
            $stringA .= "{$k}={$v}&";
        }
        $stringA = rtrim($stringA, '&');
        
        // 4. 拼接商户密钥并进行MD5加密
        $stringSignTemp = $stringA . $this->config['key'];
        return md5($stringSignTemp);
    }
    
    /**
     * 验证签名
     * @param array $params 需要验证的参数数组
     * @return bool 验证结果
     */
    public function verifySign($params) {
        $sign = $params['sign'] ?? '';
        unset($params['sign'], $params['sign_type']);
        
        $calculatedSign = $this->getSign($params);
        return $sign === $calculatedSign;
    }
    
    /**
     * 创建支付订单
     * @param string $package 套餐类型
     * @param string $price 价格
     * @param string $user 用户名
     * @param string $payType 支付方式 (alipay/wxpay)
     * @return array 支付结果
     */
    public function createPayment($package, $price, $user, $payType = 'alipay') {
        // 生成订单号
        $outTradeNo = date('YmdHis') . rand(1000, 9999);
        
        // 套餐名称映射
        $packageNames = [
            '1' => '月会员',
            '3' => '季会员', 
            'year' => '年会员',
            'forever' => '永久会员'
        ];
        
        $productName = $packageNames[$package] ?? '会员服务';
        
        // 构建请求参数
        $params = [
            'pid' => $this->config['pid'],
            'type' => $payType,
            'out_trade_no' => $outTradeNo,
            'notify_url' => $this->config['notify_url'],
            'return_url' => $this->config['return_url'],
            'name' => $productName,
            'money' => $price,
            'sitename' => $this->config['sitename'],
            'param' => json_encode(['user' => $user, 'package' => $package]),
            'clientip' => $this->getClientIP(),
            'device' => $this->getDeviceType()
        ];
        
        // 生成签名
        $params['sign'] = $this->getSign($params);
        $params['sign_type'] = 'MD5';
        
        // 发送请求
        return $this->sendRequest($this->config['api_url'], $params);
    }
    
    /**
     * 发送HTTP请求
     * @param string $url 请求地址
     * @param array $params 请求参数
     * @return array 响应结果
     */
    private function sendRequest($url, $params) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            return ['code' => 0, 'msg' => '网络请求失败'];
        }
        
        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return ['code' => 0, 'msg' => '响应数据格式错误'];
        }
        
        return $result;
    }
    
    /**
     * 获取客户端IP
     * @return string IP地址
     */
    private function getClientIP() {
        $ip = '';
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            $ip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
        }
        return $ip;
    }
    
    /**
     * 获取设备类型
     * @return string 设备类型
     */
    private function getDeviceType() {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        if (strpos($userAgent, 'MicroMessenger') !== false) {
            return 'wechat';
        } elseif (strpos($userAgent, 'AlipayClient') !== false) {
            return 'alipay';
        } elseif (strpos($userAgent, 'QQ/') !== false) {
            return 'qq';
        } elseif (preg_match('/Mobile|Android|iPhone|iPad/', $userAgent)) {
            return 'mobile';
        } else {
            return 'pc';
        }
    }
}
?>
