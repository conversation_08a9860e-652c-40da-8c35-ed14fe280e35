<?php
error_reporting(0);//抑制所有错误
header('Access-Control-Allow-Origin: *');
header('Content-type:application/json; charset=utf-8');
echo shuabu($_GET['user'],$_GET['pass'],$_GET['count']);
$appid = appid();
function shuabu($user,$pass,$count){
    if($user==''){$json = array("code"=>207,"msg"=>'账号不能为空',);return json_encode($json,JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);}
    if($pass==''){$json = array("code"=>202,"msg"=>'密码不能为空',);return json_encode($json,JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);}
    if($count==''){$json = array("code"=>205,"msg"=>'热量值不能为空',);return json_encode($json,JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);}
    if(intval($count)<= 0){$json = array("code"=>206,"msg"=>'热量值格式错误！',);return json_encode($json,JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);}
    if(intval($count)>99999){$json = array("code"=>204,"msg"=>'热量值最大限制99999',);return json_encode($json,JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);}
    
    if (strexists($user, '@')) {
    $type = 1;
    }else{
    $type= 0;
    }
    $access = login($user,$pass,$type);
    if($access == ''){
        $json = array(
            "code"=>201,
            "msg"=>'账号或密码错误',
            "type"=>$type
        );
        return json_encode($json,JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
    $user_json = get_user($access,$type);
    $user_id = get_user_id($user_json);
    $app_token = get_app_token($user_json);
	
	// $x = xban($user_id,$app_token);
	
	 $mac = mac();
	 $mad = str_replace(":", "", $mac); 
	$xxx = a($user_id,$app_token,$mac,$mad);
	$xx = b($user_id,$app_token,$mac,$mad);
	$c = c($user_id,$app_token,$mac,$mad);
	$c = d($user_id,$app_token,$mac,$mad);
	
    $return = json_decode(load($user_id,$app_token,$count,$mac,$mad),true);
    if($return['code']=='1'){
        $json = array(
            "code"=>200,
            "msg"=>'执行成功',
            "count"=>$_GET['count'],
            "type"=>$type
        );
    }else{
        $json = array(
            "code"=>208,
            "msg"=>'执行失败',
            "count"=>$_GET['count'],
            "type"=>$type
        );
    }
    return json_encode($json,JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}   
function login($user,$pass,$type){//登录,成功返回access，失败返回空
    $data_url = 'https://api-user.huami.com/registrations/'.($type == 0 ? '+86':'').$user.'/tokens';
    $post_data = array(
        "client_id" => "HuaMi",
        "country_code" => "CN",
        "password" => $pass,
        "redirect_uri" => "https%3A//s3-us-west-2.amazonaws.com/hm-registration/successsignin.html",
        "state" => "REDIRECTION",
        "token" => "access"
    );
    $return = curl_sport($data_url,$post_data,1);
    if(strexists($return,'access')){
        return GetBetween($return,'access=','&');
    }else{
        return '';
    }
}

function mac()
{
    $array = array(
		mt_rand(0x00, 0x7f),
		mt_rand(0x00, 0x7f),
        mt_rand(0x00, 0x7f),
        mt_rand(0x00, 0x7f),
        mt_rand(0x00, 0x7f),
        mt_rand(0x00, 0x7f),
        mt_rand(0x00, 0xff),
        mt_rand(0x00, 0xff)
    );
    return join(':', array_map(function ($v) {
            return sprintf("%02X", $v);
    }, $array));
	
}

function appid($length=4){
	//14位的日期（年月日时分秒）
	$date=trim(date('Ymdhis ',time()));
	//初始化变量为0
	$connt = 0;
	//建一个新数组
	$temp = array();
	while($connt < $length){
		//在一定范围内随机生成一个数放入数组中
		$temp[] = mt_rand(0, 9);
		//$data = array_unique($temp);
		//去除数组中的重复值用了“翻翻法”，就是用array_flip()把数组的key和value交换两次。这种做法比用 array_unique() 快得多。	
		$data = array_flip(array_flip($temp));
		//将数组的数量存入变量count中	
		$connt = count($data);
	}
	//为数组赋予新的键名
	shuffle($data);
	//数组转字符串
	$str=implode(",", $data);
	//替换掉逗号
	$number=str_replace(',', '', $str);
	return $date.$number;
}


function a($id,$tk,$mac,$mad){
  $url = 'https://api-mifit-cn2.zepp.com/v1/device/binds.json?r=e01fc366-b77a-4a40-99a4-24bdeebdddb3&app_time='.time().'&productVersion=-1&brandType=-1&code=0&productId=-1&activeStatus=-1&bind_timezone=32&device_source=212&device_type=0&crcedUserId=0&userid='.$id.'&appid='.$appid.'&auth_key=&brand=Xiaomi&channel=Normal&country=CN&cv=50691_6.6.2&device=android_29&deviceid=&displayName=&enableMultiDevice=true&fw_hr_version=&fw_version=&hardwareVersion=&lang=zh_CN&mac='.$mac.'&r=e01fc366-b77a-4a40-99a4-24bdeebdddb3&sn=&soft_version=&sys_model=MI+8&sys_version=Android_10&timezone=Asia%2FShanghai&v=2.0';
	
	$header = [
				'hm-privacy-diagnostics:false',
				'appplatform:android_phone',
				'hm-privacy-ceip:true',
				'clientId: '.$appid,
				'lang:zh_CN',
				'v:2.0',
				'X-Request-Id: e01fc366-b77a-4a40-99a4-24bdeebdddb3',
				'user-agent:MiFit6.6.2 (MI 8; Android 10; Density/2.75)',
				'cv:50691_6.6.2',
				'appname: com.xiaomi.hm.health',
				'channel:Normal',
				'timezone:Asia/Shanghai',
				'Host:api-mifit-cn2.zepp.com',
				'apptoken:'.$tk,
	        ];
			   $ip_long = array(
            array('607649792', '608174079'), //*********-*************
            array('**********', '**********'), //**********-**************
            array('**********', '**********'), //**********-**************
            array('**********', '**********'), //**********-**************
            array('**********', '**********'), //***********-***************
            array('-**********', '-**********'), //***********-***************
            array('-**********', '-**********'), //*********-**************
            array('-**********', '-**********'), //**********-**************
            array('-770113536', '-768606209'), //**********-**************
            array('-569376768', '-564133889'), //**********-**************
    );
    $rand_key = mt_rand(0, 9);
    $ip= long2ip(mt_rand($ip_long[$rand_key][0], $ip_long[$rand_key][1])); 
    $header[] = "CLIENT-IP:".$ip;
        $header[] = "X-FORWARDED-FOR:".$ip;
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	$output = curl_exec($ch);
	curl_close($ch);
	
			
}



function b($id,$tk){
  $url = 'https://api-mifit-cn2.zepp.com/v1/device/binds.json?r=e01fc366-b77a-4a40-99a4-24bdeebdddb3&device_type=0&userid='.$id.'&appid='.$appid.'&channel=Normal&country=CN&cv=50691_6.6.2&device=android_29&lang=zh_CN&publickeyhash=SHA1%3A1863c2cce5d159413bed92c6b563c279&r=e01fc366-b77a-4a40-99a4-24bdeebdddb3&random=ggbP40dIPZVBcFuede9%2FXnjLwk3JZE0AqzNmk7SQf5I%3D&timezone=Asia%2FShanghai&v=2.0';
	
	$header = [
				'hm-privacy-diagnostics:false',
				'appplatform:android_phone',
				'hm-privacy-ceip:true',
				'clientId: '.$appid,
				'lang:zh_CN',
				'v:2.0',
				'X-Request-Id: e01fc366-b77a-4a40-99a4-24bdeebdddb3',
				'user-agent:MiFit6.6.2 (MI 8; Android 10; Density/2.75)',
				'cv:50691_6.6.2',
				'appname: com.xiaomi.hm.health',
				'channel:Normal',
				'timezone:Asia/Shanghai',
				'Host:api-mifit-cn2.zepp.com',
				'apptoken:'.$tk,
	        ];
			   $ip_long = array(
            array('607649792', '608174079'), //*********-*************
            array('**********', '**********'), //**********-**************
            array('**********', '**********'), //**********-**************
            array('**********', '**********'), //**********-**************
            array('**********', '**********'), //***********-***************
            array('-**********', '-**********'), //***********-***************
            array('-**********', '-**********'), //*********-**************
            array('-**********', '-**********'), //**********-**************
            array('-770113536', '-768606209'), //**********-**************
            array('-569376768', '-564133889'), //**********-**************
    );
    $rand_key = mt_rand(0, 9);
    $ip= long2ip(mt_rand($ip_long[$rand_key][0], $ip_long[$rand_key][1])); 
    $header[] = "CLIENT-IP:".$ip;
        $header[] = "X-FORWARDED-FOR:".$ip;
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	$output = curl_exec($ch);
	curl_close($ch);
	
}




function c($id,$tk,$mac,$mad){
  $url = 'https://api-mifit-cn2.zepp.com/v1/device/binds.json?r=e01fc366-b77a-4a40-99a4-24bdeebdddb3';
  
  $data = 'app_time='.time().'&productVersion=256&brandType=-1&code=0&productId=91&activeStatus=1&bind_timezone=32&device_source=211&device_type=0&crcedUserId=0&userid='.$id.'&appid='.$appid.'&auth_key=42cd33e89a5c08de1d878fb4e31340fg&brand=Xiaomi&channel=Normal&country=CN&cv=50691_6.6.2&device=android_29&deviceid='.$mad.'&displayName=&enableMultiDevice=true&fw_hr_version=&fw_version=V1.0.6.20&hardwareVersion=V0.82.131.19&lang=zh_CN&mac='.$mac.'&sn=32098%2F10777460&soft_version=6.6.2&sys_model=MI+8&sys_version=Android_10&timezone=Asia%2FShanghai&v=2.0';
	
	$header = [
				'hm-privacy-diagnostics:false',
				'appplatform:android_phone',
				'hm-privacy-ceip:true',
				'clientId: '.$appid,
				'lang:zh_CN',
				'v:2.0',
				'X-Request-Id: e01fc366-b77a-4a40-99a4-24bdeebdddb3',
				'user-agent:MiFit6.6.2 (MI 8; Android 10; Density/2.75)',
				'cv:50691_6.6.2',
				'appname: com.xiaomi.hm.health',
				'channel:Normal',
				'timezone:Asia/Shanghai',
				'Host:api-mifit-cn2.zepp.com',
				'apptoken:'.$tk,
	        ];
			
	$useragent = 'MiFit6.6.2 (MI 8; Android 10; Density/2.75)';
   		   $ip_long = array(
            array('607649792', '608174079'), //*********-*************
            array('**********', '**********'), //**********-**************
            array('**********', '**********'), //**********-**************
            array('**********', '**********'), //**********-**************
            array('**********', '**********'), //***********-***************
            array('-**********', '-**********'), //***********-***************
            array('-**********', '-**********'), //*********-**************
            array('-**********', '-**********'), //**********-**************
            array('-770113536', '-768606209'), //**********-**************
            array('-569376768', '-564133889'), //**********-**************
    );
    $rand_key = mt_rand(0, 9);
    $ip= long2ip(mt_rand($ip_long[$rand_key][0], $ip_long[$rand_key][1])); 
    $header[] = "CLIENT-IP:".$ip;
        $header[] = "X-FORWARDED-FOR:".$ip;
       $timeout= 60;
       $ch = curl_init();
       curl_setopt($ch, CURLOPT_URL, $url);
       curl_setopt($ch, CURLOPT_POST, true);
       curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
       curl_setopt($ch, CURLOPT_FAILONERROR, true);
       curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
       //不取得返回头信息	
       curl_setopt($ch, CURLOPT_HEADER, 0);
       // 关闭https验证
       curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
       curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
       curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true );
       curl_setopt($ch, CURLOPT_ENCODING, "" );
       curl_setopt($ch, CURLOPT_RETURNTRANSFER, true );
       curl_setopt($ch, CURLOPT_AUTOREFERER, true );
       curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout );
       curl_setopt($ch, CURLOPT_TIMEOUT, $timeout );
       curl_setopt($ch, CURLOPT_MAXREDIRS, 10 );
       curl_setopt($ch, CURLOPT_USERAGENT, $useragent);
       $content = curl_exec($ch);
       curl_close($ch);
			
}



function d($id,$tk,$mac,$mad){
       $url = 'https://api-mifit-cn2.zepp.com/users/'.$id.'/'.'devices/'.$mad;
           $data = array(
       	"deviceType"=>0,"activeStatus"=>1,"displayName"=>"极致手环","deviceId"=>"$mad"
           );
       	$headers = [
			'hm-privacy-diagnostics:false',
				'appplatform:android_phone',
				'hm-privacy-ceip:true',
				'clientid:'.$appid,
				'content-type:application/json',
				'content-length:92',
				'x-request-id:e01fc366-b77a-4a40-99a4-24bdeebdddb3',
				'lang:zh_CN',
				'v:2.0',
				'user-agent:MiFit6.6.2 (MI 8; Android 10; Density/2.75)',
				'cv:50691_6.6.2',
				'channel:Normal',
				'timezone:Asia/Shanghai',
			'Host:api-mifit-cn2.zepp.com',
			'apptoken: ' .$tk,
			'appname:com.xiaomi.hm.health'
       	    ];
       	    $ip_long = array(
            array('607649792', '608174079'), //*********-*************
            array('**********', '**********'), //**********-**************
            array('**********', '**********'), //**********-**************
            array('**********', '**********'), //**********-**************
            array('**********', '**********'), //***********-***************
            array('-**********', '-**********'), //***********-***************
            array('-**********', '-**********'), //*********-**************
            array('-**********', '-**********'), //**********-**************
            array('-770113536', '-768606209'), //**********-**************
            array('-569376768', '-564133889'), //**********-**************
    );
    $rand_key = mt_rand(0, 9);
    $ip= long2ip(mt_rand($ip_long[$rand_key][0], $ip_long[$rand_key][1])); 
    $headers[] = "CLIENT-IP:".$ip;
        $headers[] = "X-FORWARDED-FOR:".$ip;
        $data = json_encode($data,320);
		$useragent = 'MiFit6.6.2 (MI 8; Android 10; Density/2.75)';
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url); 
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT"); 
        curl_setopt($ch, CURLOPT_HEADER, 0); //定义是否显示状态头 1：显示 ； 0：不显示 
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers); //定义header
         curl_setopt($ch, CURLOPT_USERAGENT, $useragent);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data); //定义提交的数据
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); //定义是否直接输出返回流 
        $res = curl_exec($ch);
        curl_close($ch); //关闭
    }




function get_user($access,$type){//获取用户信息，json类型
    $data_url = 'https://account.huami.com/v2/client/login';
    $post_data="allow_registration=false&app_name=com.xiaomi.hm.health&app_version=4.1.0&code=".$access."&country_code=CN&device_id=device_id_type=uuid&device_model=phone&dn=api-user.huami.com%2Capi-mifit.huami.com%2Capp-analytics.huami.com&grant_type=access_token&lang=zh_CN&os_version=1.5.0&source=com.xiaomi.hm.health&third_name=".($type == 0 ? "huami_phone" : "email");
    $return = curl_sport($data_url,$post_data,0);
    return $return;     
}
function load($user_id,$app_token,$count,$mac,$mad){//提交步数
    $data_url = "https://api-mifit-cn.huami.com/v1/data/band_data.json?&t=" . time();
    $headers = [
            'apptoken: ' . $app_token,
            'User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 13_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/7.0.12(0x17000c2d) NetType/WIFI Language/zh_CN'
        ];
    $json = '[{"data_hr":"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","date":"' . date('Y-m-d', time()) . '","data":[{"start":0,"stop":1439,"value":"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","tz":32,"did":"'.$mad.'","src":24}],"summary":"{\"v\":6,\"slp\":{\"st\":1628296479,\"ed\":1628296479,\"dp\":0,\"lt\":0,\"wk\":0,\"usrSt\":-1440,\"usrEd\":-1440,\"wc\":0,\"is\":0,\"lb\":0,\"to\":0,\"dt\":0,\"rhr\":0,\"ss\":0},\"stp\":{\"ttl\":' . $count . ',\"dis\":10627,\"cal\":510,\"wk\":41,\"rn\":50,\"runDist\":7654,\"runCal\":397,\"stage\":[{\"start\":327,\"stop\":341,\"mode\":1,\"dis\":481,\"cal\":13,\"step\":680},{\"start\":342,\"stop\":367,\"mode\":3,\"dis\":2295,\"cal\":95,\"step\":2874},{\"start\":368,\"stop\":377,\"mode\":4,\"dis\":1592,\"cal\":88,\"step\":1664},{\"start\":378,\"stop\":386,\"mode\":3,\"dis\":1072,\"cal\":51,\"step\":1245},{\"start\":387,\"stop\":393,\"mode\":4,\"dis\":1036,\"cal\":57,\"step\":1124},{\"start\":394,\"stop\":398,\"mode\":3,\"dis\":488,\"cal\":19,\"step\":607},{\"start\":399,\"stop\":414,\"mode\":4,\"dis\":2220,\"cal\":120,\"step\":2371},{\"start\":415,\"stop\":427,\"mode\":3,\"dis\":1268,\"cal\":59,\"step\":1489},{\"start\":428,\"stop\":433,\"mode\":1,\"dis\":152,\"cal\":4,\"step\":238},{\"start\":434,\"stop\":444,\"mode\":3,\"dis\":2295,\"cal\":95,\"step\":2874},{\"start\":445,\"stop\":455,\"mode\":4,\"dis\":1592,\"cal\":88,\"step\":1664},{\"start\":456,\"stop\":466,\"mode\":3,\"dis\":1072,\"cal\":51,\"step\":1245},{\"start\":467,\"stop\":477,\"mode\":4,\"dis\":1036,\"cal\":57,\"step\":1124},{\"start\":478,\"stop\":488,\"mode\":3,\"dis\":488,\"cal\":19,\"step\":607},{\"start\":489,\"stop\":499,\"mode\":4,\"dis\":2220,\"cal\":120,\"step\":2371},{\"start\":500,\"stop\":511,\"mode\":3,\"dis\":1268,\"cal\":59,\"step\":1489},{\"start\":512,\"stop\":522,\"mode\":1,\"dis\":152,\"cal\":4,\"step\":238}]},\"goal\":8000,\"tz\":\"28800\"}","source":24,"type":0}]';
    $data = [
            'data_json' => $json,
            'userid' => $userid,
            'device_type' => '0',
            'last_sync_data_time' => '1597306380',
            'last_deviceid' => "$mad",
        ];
    $return=Curl($data_url, $data, null, $headers);
    $return2=json_encode(array('code'=>1,'msg'=>'执行成功'));
    return $return2;
}
function get_user_id($user_json){//解析user_json，获取user_id
    $json = json_decode($user_json,true);
    return $json['token_info']['user_id'];      
}
function get_app_token($user_json){//解析user_json，获取app_token
    $json = json_decode($user_json,true);
    return $json['token_info']['app_token'];        
}
//取出文本中间文本
function GetBetween($content,$start,$end){
    $r = explode($start, $content);
    if (isset($r[1])){
        $r = explode($end, $r[1]);
        return $r[0];
    }
    return '';
}


//此应用专用curl，返回response_header
function curl_sport($url,$post=0,$type=0,$request=''){//GET\POST网络请求
$ip_long = array(
            array('607649792', '608174079'), //*********-*************
            array('**********', '**********'), //**********-**************
            array('**********', '**********'), //**********-**************
            array('**********', '**********'), //**********-**************
            array('**********', '**********'), //***********-***************
            array('-**********', '-**********'), //***********-***************
            array('-**********', '-**********'), //*********-**************
            array('-**********', '-**********'), //**********-**************
            array('-770113536', '-768606209'), //**********-**************
            array('-569376768', '-564133889'), //**********-**************
    );
    $rand_key = mt_rand(0, 9);
    $ip= long2ip(mt_rand($ip_long[$rand_key][0], $ip_long[$rand_key][1])); 
    $httpheader[] = "CLIENT-IP:".$ip;
        $httpheader[] = "X-FORWARDED-FOR:".$ip;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL,$url);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $httpheader);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);  
    if($post){
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
    }
    if($request!=''){
        $httpheader[] = "apptoken:".$request;
        
        curl_setopt($ch, CURLOPT_HTTPHEADER, $httpheader);
    }
    //返回 response_header, 该选项非常重要,如果不为 true, 只会获得响应的正文
    curl_setopt($ch, CURLOPT_HEADER, $type);
    curl_setopt($ch, CURLINFO_HEADER_OUT, true);
    curl_setopt($ch, CURLOPT_AUTOREFERER, false);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER,1);
    $ret = curl_exec($ch);
    curl_close($ch);
    return $ret;
}

function strexists($string, $find) {//取字符串中是否包含指定字符串
    return !(strpos($string, $find) === FALSE);
}

function Curl($url, $data = null, $cookie = null, $httpheader = null)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        $httpheader[] = "Accept-Language: zh-CN,zh;q=0.8";
        $httpheader[] = "Connection: keep-alive";
        curl_setopt($curl, CURLOPT_HTTPHEADER, $httpheader);
        if ($data) {
            if (is_array($data)) $data = http_build_query($data);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
            curl_setopt($curl, CURLOPT_POST, 1);
        }
        if($cookie){
            curl_setopt($curl, CURLOPT_COOKIE, $cookie);
        }
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($curl, CURLOPT_HEADER, 1);
        $ret = curl_exec($curl);
        $headerSize = curl_getinfo($curl, CURLINFO_HEADER_SIZE);
        $header = substr($ret, 0, $headerSize);
        $content = substr($ret, $headerSize);
        $ret = array();
        $ret['header'] = $header;
        $ret['content'] = $content;
        curl_close($curl);
        return $ret;
    }
?>