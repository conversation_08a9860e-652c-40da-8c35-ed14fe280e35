<?php
// 验证管理员登录状态
function checkAdminLogin() {
    if (!isset($_SESSION[ADMIN_SESSION_NAME])) {
        header('Location: login.php');
        exit();
    }
    
    // 检查会话超时
    if (isset($_SESSION[ADMIN_SESSION_NAME]['login_time'])) {
        if (time() - $_SESSION[ADMIN_SESSION_NAME]['login_time'] > ADMIN_TIMEOUT) {
            session_destroy();
            header('Location: login.php?timeout=1');
            exit();
        }
    }
}

// 获取当前登录的管理员信息
function getCurrentAdmin() {
    return $_SESSION[ADMIN_SESSION_NAME] ?? null;
}

// 管理员登出
function adminLogout() {
    session_destroy();
    header('Location: login.php');
    exit();
}
?>