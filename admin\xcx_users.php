<?php
session_start();
require_once '../config/config.php';
require_once 'auth.php';

// 验证登录状态
checkAdminLogin();

// 处理删除用户
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['user'])) {
    header('Content-Type: application/json');
    try {
        $db = getDB();
        $stmt = $db->prepare("DELETE FROM xcxuser WHERE user = ?");
        $stmt->execute([$_GET['user']]);
        echo json_encode(['code' => 0, 'msg' => '删除成功']);
        exit;
    } catch (Exception $e) {
        echo json_encode(['code' => 1, 'msg' => '删除失败: ' . $e->getMessage()]);
        exit;
    }
}

// 处理添加/编辑用户
if ($_POST) {
    header('Content-Type: application/json');
    $action = $_POST['action'];
    $user = $_POST['user'];
    $zt = $_POST['zt'];
    
    try {
        $db = getDB();
        
        if ($action == 'add') {
            $stmt = $db->prepare("INSERT INTO xcxuser (user, zt) VALUES (?, ?)");
            $stmt->execute([$user, $zt]);
            echo json_encode(['code' => 0, 'msg' => '添加成功']);
        } else if ($action == 'edit') {
            $oldUser = $_POST['old_user'];
            $stmt = $db->prepare("UPDATE xcxuser SET user=?, zt=? WHERE user=?");
            $stmt->execute([$user, $zt, $oldUser]);
            echo json_encode(['code' => 0, 'msg' => '修改成功']);
        }
    } catch (Exception $e) {
        echo json_encode(['code' => 1, 'msg' => '操作失败: ' . $e->getMessage()]);
    }
    exit;
}

// 获取用户列表
if (isset($_GET['json'])) {
    header('Content-Type: application/json');
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $offset = ($page - 1) * $limit;

    try {
        $db = getDB();
        
        // 获取总数
        $stmt = $db->query("SELECT COUNT(*) as total FROM xcxuser");
        $total = $stmt->fetch()['total'];
        
        // 获取用户列表 - 直接拼接数值避免参数绑定问题
        $sql = "SELECT user, zt FROM xcxuser LIMIT $offset, $limit";
        $stmt = $db->query($sql);
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'code' => 0,
            'msg' => '',
            'count' => $total,
            'data' => $users
        ]);
        exit;
    } catch (Exception $e) {
        echo json_encode([
            'code' => 1,
            'msg' => '获取数据失败: ' . $e->getMessage(),
            'count' => 0,
            'data' => []
        ]);
        exit;
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>小程序用户管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://unpkg.com/layui@2.8.18/dist/css/layui.css" rel="stylesheet">
</head>
<body>
    <div style="padding: 15px;">
        <div class="layui-card">
            <div class="layui-card-header">
                <h3>小程序用户管理</h3>
                <div class="layui-btn-group">
                    <button class="layui-btn layui-btn-sm" onclick="addUser()">
                        <i class="layui-icon layui-icon-add-1"></i> 添加用户
                    </button>
                    <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="refreshTable()">
                        <i class="layui-icon layui-icon-refresh"></i> 刷新
                    </button>
                </div>
            </div>
            <div class="layui-card-body">
                <table class="layui-hide" id="xcxUserTable" lay-filter="xcxUserTable"></table>
            </div>
        </div>
    </div>

    <!-- 操作按钮模板 -->
    <script type="text/html" id="barTpl">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
    </script>

    <!-- 状态模板 -->
    <script type="text/html" id="ztTpl">
        {{# if(d.zt == 1){ }}
            <span class="layui-badge layui-bg-green">启用</span>
        {{# } else { }}
            <span class="layui-badge">禁用</span>
        {{# } }}
    </script>

    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script>
    layui.use(['table', 'layer', 'form'], function(){
        var table = layui.table;
        var layer = layui.layer;
        var form = layui.form;
        
        // 渲染表格
        table.render({
            elem: '#xcxUserTable',
            url: 'xcx_users.php?json=1',
            page: true,
            cols: [[
                {field: 'user', title: '用户标识', width: 300},
                {field: 'zt', title: '状态', width: 120, templet: '#ztTpl'},
                {title: '操作', toolbar: '#barTpl', width: 150}
            ]]
        });
        
        // 监听工具条
        table.on('tool(xcxUserTable)', function(obj){
            var data = obj.data;
            if(obj.event === 'del'){
                layer.confirm('确定删除这个用户吗？', function(index){
                    layer.close(index);
                    
                    layer.load();
                    fetch('xcx_users.php?action=delete&user=' + encodeURIComponent(data.user))
                    .then(response => response.json())
                    .then(result => {
                        layer.closeAll('loading');
                        if(result.code === 0) {
                            layer.msg('删除成功', {icon: 1});
                            obj.del();
                        } else {
                            layer.msg(result.msg, {icon: 2});
                        }
                    });
                });
            } else if(obj.event === 'edit'){
                editUser(data);
            }
        });
        
        // 添加用户
        window.addUser = function() {
            showUserForm('add', {});
        }
        
        // 编辑用户
        function editUser(data) {
            showUserForm('edit', data);
        }
        
        // 显示用户表单
        function showUserForm(action, data) {
            var title = action === 'add' ? '添加小程序用户' : '编辑小程序用户';
            var content = `
                <form class="layui-form" style="padding: 20px;">
                    <input type="hidden" name="action" value="${action}">
                    ${action === 'edit' ? `<input type="hidden" name="old_user" value="${data.user || ''}">` : ''}
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">用户标识</label>
                        <div class="layui-input-block">
                            <input type="text" name="user" required lay-verify="required" 
                                   placeholder="请输入用户标识" class="layui-input" value="${data.user || ''}">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-block">
                            <select name="zt" lay-verify="required">
                                <option value="">请选择状态</option>
                                <option value="1" ${data.zt == 1 ? 'selected' : ''}>启用</option>
                                <option value="0" ${data.zt == 0 ? 'selected' : ''}>禁用</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="userForm">确定</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </form>
            `;
            
            layer.open({
                type: 1,
                title: title,
                area: ['400px', '300px'],
                content: content,
                success: function(layero, index) {
                    form.render();
                    
                    // 监听提交
                    form.on('submit(userForm)', function(formData){
                        layer.load();
                        
                        fetch('xcx_users.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: new URLSearchParams(formData.field)
                        })
                        .then(response => response.json())
                        .then(result => {
                            layer.closeAll('loading');
                            if(result.code === 0) {
                                layer.msg(result.msg, {icon: 1});
                                layer.close(index);
                                table.reload('xcxUserTable');
                            } else {
                                layer.msg(result.msg, {icon: 2});
                            }
                        });
                        
                        return false;
                    });
                }
            });
        }
        
        // 刷新表格
        window.refreshTable = function() {
            table.reload('xcxUserTable');
        }
    });
    </script>
</body>
</html>


