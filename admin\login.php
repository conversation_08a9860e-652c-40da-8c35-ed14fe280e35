<?php
session_start();
require_once '../config/config.php';

// 如果已经登录，跳转到后台首页
if (isset($_SESSION[ADMIN_SESSION_NAME])) {
    header('Location: index.php');
    exit();
}

$error = '';

if ($_POST) {
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    
    if (empty($username) || empty($password)) {
        $error = '用户名和密码不能为空';
    } else {
        try {
            $db = getDB();
            $stmt = $db->prepare("SELECT id, username, password FROM admin_users WHERE username = ?");
            $stmt->execute([$username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && password_verify($password, $user['password'])) {
                $_SESSION[ADMIN_SESSION_NAME] = [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'login_time' => time()
                ];
                header('Location: index.php');
                exit();
            } else {
                $error = '用户名或密码错误';
            }
        } catch (Exception $e) {
            $error = '登录失败，请稍后重试';
        }
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>运动步数管理系统 - 登录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://unpkg.com/layui@2.8.18/dist/css/layui.css" rel="stylesheet">
    <style>
        .login-container {
            position: absolute;
            left: 50%;
            top: 50%;
            width: 400px;
            margin-left: -200px;
            margin-top: -200px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 20px;
        }
        .login-header h1 {
            color: #1E9FFF;
            font-size: 28px;
        }
    </style>
</head>
<body style="background: #f2f2f2;">
    <div class="login-container">
        <div class="login-header">
            <h1>运动步数管理系统</h1>
        </div>
        
        <div class="layui-card">
            <div class="layui-card-body" style="padding: 30px;">
                <?php if ($error): ?>
                    <div class="layui-elem-quote layui-quote-nm layui-bg-red" style="color: white;">
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <form class="layui-form" method="POST">
                    <div class="layui-form-item">
                        <label class="layui-form-label">用户名</label>
                        <div class="layui-input-block">
                            <input type="text" name="username" required lay-verify="required" 
                                   placeholder="请输入用户名" autocomplete="off" class="layui-input"
                                   value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">密码</label>
                        <div class="layui-input-block">
                            <input type="password" name="password" required lay-verify="required" 
                                   placeholder="请输入密码" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="formDemo">登录</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script>
    layui.use(['form', 'layer'], function(){
        var form = layui.form;
        var layer = layui.layer;
        
        // 监听提交
        form.on('submit(formDemo)', function(data){
            return true; // 允许表单提交
        });
    });
    </script>
</body>
</html>
