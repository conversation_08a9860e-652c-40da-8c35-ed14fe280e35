<?php
session_start();
require_once '../config/config.php';
require_once 'auth.php';

// 验证登录状态
checkAdminLogin();

// 处理删除管理员
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    header('Content-Type: application/json');
    try {
        $db = getDB();
        $stmt = $db->prepare("DELETE FROM admin_users WHERE id = ?");
        $stmt->execute([$_GET['id']]);
        echo json_encode(['code' => 0, 'msg' => '删除成功']);
        exit;
    } catch (Exception $e) {
        echo json_encode(['code' => 1, 'msg' => '删除失败: ' . $e->getMessage()]);
        exit;
    }
}

// 处理添加/编辑管理员
if ($_POST) {
    header('Content-Type: application/json');
    $action = $_POST['action'];
    $username = $_POST['username'];
    $email = $_POST['email'];
    $password = $_POST['password'] ?? '';
    
    try {
        $db = getDB();
        
        if ($action == 'add') {
            if (empty($password)) {
                echo json_encode(['code' => 1, 'msg' => '密码不能为空']);
                exit;
            }
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $db->prepare("INSERT INTO admin_users (username, password, email) VALUES (?, ?, ?)");
            $stmt->execute([$username, $hashedPassword, $email]);
            echo json_encode(['code' => 0, 'msg' => '添加成功']);
        } else if ($action == 'edit') {
            $id = $_POST['id'];
            if (!empty($password)) {
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $db->prepare("UPDATE admin_users SET username=?, password=?, email=? WHERE id=?");
                $stmt->execute([$username, $hashedPassword, $email, $id]);
            } else {
                $stmt = $db->prepare("UPDATE admin_users SET username=?, email=? WHERE id=?");
                $stmt->execute([$username, $email, $id]);
            }
            echo json_encode(['code' => 0, 'msg' => '修改成功']);
        }
    } catch (Exception $e) {
        echo json_encode(['code' => 1, 'msg' => '操作失败: ' . $e->getMessage()]);
    }
    exit;
}

// 获取管理员列表
if (isset($_GET['json'])) {
    header('Content-Type: application/json');
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $offset = ($page - 1) * $limit;

    try {
        $db = getDB();
        
        // 获取总数
        $stmt = $db->query("SELECT COUNT(*) as total FROM admin_users");
        $total = $stmt->fetch()['total'];
        
        // 获取管理员列表
        $sql = "SELECT id, username, email, created_at FROM admin_users LIMIT $offset, $limit";
        $stmt = $db->query($sql);
        $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'code' => 0,
            'msg' => '',
            'count' => $total,
            'data' => $admins
        ]);
        exit;
    } catch (Exception $e) {
        echo json_encode([
            'code' => 1,
            'msg' => '获取数据失败: ' . $e->getMessage(),
            'count' => 0,
            'data' => []
        ]);
        exit;
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>管理员管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://unpkg.com/layui@2.8.18/dist/css/layui.css" rel="stylesheet">
</head>
<body>
    <div style="padding: 15px;">
        <div class="layui-card">
            <div class="layui-card-header">
                <h3>管理员管理</h3>
                <div class="layui-btn-group">
                    <button class="layui-btn layui-btn-sm" onclick="addAdmin()">
                        <i class="layui-icon layui-icon-add-1"></i> 添加管理员
                    </button>
                    <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="refreshTable()">
                        <i class="layui-icon layui-icon-refresh"></i> 刷新
                    </button>
                </div>
            </div>
            <div class="layui-card-body">
                <table class="layui-hide" id="adminTable" lay-filter="adminTable"></table>
            </div>
        </div>
    </div>

    <!-- 操作按钮模板 -->
    <script type="text/html" id="barTpl">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
    </script>

    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script>
    layui.use(['table', 'layer', 'form'], function(){
        var table = layui.table;
        var layer = layui.layer;
        var form = layui.form;
        
        // 渲染表格
        table.render({
            elem: '#adminTable',
            url: 'admin_users.php?json=1',
            page: true,
            cols: [[
                {field: 'id', title: 'ID', width: 80},
                {field: 'username', title: '用户名', width: 150},
                {field: 'email', title: '邮箱', width: 200},
                {field: 'created_at', title: '创建时间', width: 180},
                {title: '操作', toolbar: '#barTpl', width: 150}
            ]],
            response: {
                statusName: 'code',
                statusCode: 0,
                msgName: 'msg',
                countName: 'count',
                dataName: 'data'
            }
        });
        
        // 监听工具条
        table.on('tool(adminTable)', function(obj){
            var data = obj.data;
            if(obj.event === 'del'){
                layer.confirm('确定删除这个管理员吗？', function(index){
                    layer.close(index);
                    
                    layer.load();
                    fetch('admin_users.php?action=delete&id=' + data.id)
                    .then(response => response.json())
                    .then(result => {
                        layer.closeAll('loading');
                        if(result.code === 0) {
                            layer.msg('删除成功', {icon: 1});
                            obj.del();
                        } else {
                            layer.msg(result.msg, {icon: 2});
                        }
                    })
                    .catch(error => {
                        layer.closeAll('loading');
                        layer.msg('请求失败: ' + error.message, {icon: 2});
                    });
                });
            } else if(obj.event === 'edit'){
                editAdmin(data);
            }
        });
        
        // 添加管理员
        window.addAdmin = function() {
            showAdminForm('add', {});
        }
        
        // 编辑管理员
        function editAdmin(data) {
            showAdminForm('edit', data);
        }
        
        // 显示管理员表单
        function showAdminForm(action, data) {
            var title = action === 'add' ? '添加管理员' : '编辑管理员';
            var content = `
                <form class="layui-form" style="padding: 20px;">
                    <input type="hidden" name="action" value="${action}">
                    ${action === 'edit' ? `<input type="hidden" name="id" value="${data.id || ''}">` : ''}
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">用户名</label>
                        <div class="layui-input-block">
                            <input type="text" name="username" required lay-verify="required" 
                                   placeholder="请输入用户名" class="layui-input" value="${data.username || ''}">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">邮箱</label>
                        <div class="layui-input-block">
                            <input type="email" name="email" required lay-verify="required|email" 
                                   placeholder="请输入邮箱" class="layui-input" value="${data.email || ''}">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">密码</label>
                        <div class="layui-input-block">
                            <input type="password" name="password" ${action === 'add' ? 'required lay-verify="required"' : ''} 
                                   placeholder="${action === 'add' ? '请输入密码' : '留空则不修改密码'}" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="adminForm">确定</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </form>
            `;
            
            layer.open({
                type: 1,
                title: title,
                area: ['500px', '400px'],
                content: content,
                success: function(layero, index) {
                    form.render();
                    
                    // 监听提交
                    form.on('submit(adminForm)', function(formData){
                        layer.load();
                        
                        fetch('admin_users.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: new URLSearchParams(formData.field)
                        })
                        .then(response => response.json())
                        .then(result => {
                            layer.closeAll('loading');
                            if(result.code === 0) {
                                layer.msg(result.msg, {icon: 1});
                                layer.close(index);
                                table.reload('adminTable');
                            } else {
                                layer.msg(result.msg, {icon: 2});
                            }
                        })
                        .catch(error => {
                            layer.closeAll('loading');
                            layer.msg('请求失败: ' + error.message, {icon: 2});
                        });
                        
                        return false;
                    });
                }
            });
        }
        
        // 刷新表格
        window.refreshTable = function() {
            table.reload('adminTable');
        }
    });
    </script>
</body>
</html>