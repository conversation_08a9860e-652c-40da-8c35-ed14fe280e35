<?php
session_start();
require_once '../config/config.php';
require_once 'auth.php';

// 验证登录状态
checkAdminLogin();

// 处理删除配置
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    header('Content-Type: application/json');
    try {
        $db = getDB();
        $stmt = $db->prepare("DELETE FROM syskey WHERE id = ?");
        $stmt->execute([$_GET['id']]);
        echo json_encode(['code' => 0, 'msg' => '删除成功']);
        exit;
    } catch (Exception $e) {
        echo json_encode(['code' => 1, 'msg' => '删除失败: ' . $e->getMessage()]);
        exit;
    }
}

// 处理添加/编辑配置
if ($_POST) {
    header('Content-Type: application/json');
    $action = $_POST['action'];
    $appid = $_POST['appid'];
    $appkey = $_POST['appkey'];
    $qzkg = $_POST['qzkg'] ?? 0;
    $qzbl = $_POST['qzbl'] ?? 0;
    $ggid = $_POST['ggid'] ?? '';
    
    try {
        $db = getDB();
        
        if ($action == 'add') {
            $stmt = $db->prepare("INSERT INTO syskey (appid, appkey, qzkg, qzbl, ggid) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$appid, $appkey, $qzkg, $qzbl, $ggid]);
            echo json_encode(['code' => 0, 'msg' => '添加成功']);
        } else if ($action == 'edit') {
            $id = $_POST['id'];
            $stmt = $db->prepare("UPDATE syskey SET appid=?, appkey=?, qzkg=?, qzbl=?, ggid=? WHERE id=?");
            $stmt->execute([$appid, $appkey, $qzkg, $qzbl, $ggid, $id]);
            echo json_encode(['code' => 0, 'msg' => '修改成功']);
        }
    } catch (Exception $e) {
        echo json_encode(['code' => 1, 'msg' => '操作失败: ' . $e->getMessage()]);
    }
    exit;
}

// 获取配置列表
if (isset($_GET['json'])) {
    header('Content-Type: application/json');
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $offset = ($page - 1) * $limit;

    try {
        $db = getDB();
        
        // 获取总数
        $stmt = $db->query("SELECT COUNT(*) as total FROM syskey");
        $total = $stmt->fetch()['total'];
        
        // 获取配置列表
        $sql = "SELECT id, appid, appkey, qzkg, qzbl, ggid FROM syskey LIMIT $offset, $limit";
        $stmt = $db->query($sql);
        $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'code' => 0,
            'msg' => '',
            'count' => $total,
            'data' => $configs
        ]);
        exit;
    } catch (Exception $e) {
        echo json_encode([
            'code' => 1,
            'msg' => '获取数据失败: ' . $e->getMessage(),
            'count' => 0,
            'data' => []
        ]);
        exit;
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>系统配置</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://unpkg.com/layui@2.8.18/dist/css/layui.css" rel="stylesheet">
    <style>
        .layui-table-cell {
            height: auto !important;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .layui-table-view {
            margin: 0;
        }
        .layui-card-body {
            padding: 10px;
        }
    </style>
</head>
<body>
    <div style="padding: 15px;">
        <div class="layui-card">
            <div class="layui-card-header">
                <h3>系统配置</h3>
                <div class="layui-btn-group">
                    <button class="layui-btn layui-btn-sm" onclick="addConfig()">
                        <i class="layui-icon layui-icon-add-1"></i> 添加配置
                    </button>
                    <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="refreshTable()">
                        <i class="layui-icon layui-icon-refresh"></i> 刷新
                    </button>
                </div>
            </div>
            <div class="layui-card-body">
                <div style="overflow-x: auto;">
                    <table class="layui-hide" id="configTable" lay-filter="configTable"></table>
                </div>
            </div>
        </div>
    </div>

    <!-- 强制开关状态模板 -->
    <script type="text/html" id="qzkgTpl">
        {{# if(d.qzkg == 1){ }}
            <span class="layui-badge layui-bg-green">开启</span>
        {{# } else { }}
            <span class="layui-badge">关闭</span>
        {{# } }}
    </script>

    <!-- 操作按钮模板 -->
    <script type="text/html" id="barTpl">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
    </script>

    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script>
    layui.use(['table', 'layer', 'form'], function(){
        var table = layui.table;
        var layer = layui.layer;
        var form = layui.form;
        
        // 渲染表格
        table.render({
            elem: '#configTable',
            url: 'system_config.php?json=1',
            page: true,
            autoSort: false,
            cellMinWidth: 80,
            cols: [[
                {field: 'id', title: 'ID', width: 60, sort: true},
                {field: 'appid', title: 'APP ID', minWidth: 120},
                {field: 'appkey', title: 'APP Key', minWidth: 150, templet: function(d){
                    if (!d.appkey) return '';
                    return '<span title="' + d.appkey + '">' + 
                           (d.appkey.length > 20 ? d.appkey.substring(0, 20) + '...' : d.appkey) + 
                           '</span>';
                }},
                {field: 'qzkg', title: '强制开关', width: 80, templet: '#qzkgTpl'},
                {field: 'qzbl', title: '强制比例', width: 80},
                {field: 'ggid', title: '广告ID', minWidth: 100, templet: function(d){
                    if (!d.ggid) return '';
                    return '<span title="' + d.ggid + '">' + 
                           (d.ggid.length > 15 ? d.ggid.substring(0, 15) + '...' : d.ggid) + 
                           '</span>';
                }},
                {title: '操作', toolbar: '#barTpl', width: 120, fixed: 'right'}
            ]],
            response: {
                statusName: 'code',
                statusCode: 0,
                msgName: 'msg',
                countName: 'count',
                dataName: 'data'
            }
        });
        
        // 监听工具条
        table.on('tool(configTable)', function(obj){
            var data = obj.data;
            if(obj.event === 'del'){
                layer.confirm('确定删除这个配置吗？', function(index){
                    layer.close(index);
                    
                    layer.load();
                    fetch('system_config.php?action=delete&id=' + data.id)
                    .then(response => response.json())
                    .then(result => {
                        layer.closeAll('loading');
                        if(result.code === 0) {
                            layer.msg('删除成功', {icon: 1});
                            obj.del();
                        } else {
                            layer.msg(result.msg, {icon: 2});
                        }
                    })
                    .catch(error => {
                        layer.closeAll('loading');
                        layer.msg('请求失败: ' + error.message, {icon: 2});
                    });
                });
            } else if(obj.event === 'edit'){
                editConfig(data);
            }
        });
        
        // 添加配置
        window.addConfig = function() {
            showConfigForm('add', {});
        }
        
        // 编辑配置
        function editConfig(data) {
            showConfigForm('edit', data);
        }
        
        // 显示配置表单
        function showConfigForm(action, data) {
            var title = action === 'add' ? '添加配置' : '编辑配置';
            var content = `
                <form class="layui-form" style="padding: 20px;">
                    <input type="hidden" name="action" value="${action}">
                    ${action === 'edit' ? `<input type="hidden" name="id" value="${data.id || ''}">` : ''}
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">APP ID</label>
                        <div class="layui-input-block">
                            <input type="text" name="appid" required lay-verify="required" 
                                   placeholder="请输入APP ID" class="layui-input" value="${data.appid || ''}">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">APP Key</label>
                        <div class="layui-input-block">
                            <textarea name="appkey" required lay-verify="required" 
                                      placeholder="请输入APP Key" class="layui-textarea">${data.appkey || ''}</textarea>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">强制开关</label>
                        <div class="layui-input-block">
                            <select name="qzkg" lay-verify="required">
                                <option value="">请选择状态</option>
                                <option value="1" ${data.qzkg == 1 ? 'selected' : ''}>开启</option>
                                <option value="0" ${data.qzkg == 0 ? 'selected' : ''}>关闭</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">强制比例</label>
                        <div class="layui-input-block">
                            <select name="qzbl" lay-verify="required">
                                <option value="">请选择比例</option>
                                <option value="1" ${data.qzbl == 1 ? 'selected' : ''}>1</option>
                                <option value="2" ${data.qzbl == 2 ? 'selected' : ''}>2</option>
                                <option value="3" ${data.qzbl == 3 ? 'selected' : ''}>3</option>
                                <option value="4" ${data.qzbl == 4 ? 'selected' : ''}>4</option>
                                <option value="5" ${data.qzbl == 5 ? 'selected' : ''}>5</option>
                                <option value="6" ${data.qzbl == 6 ? 'selected' : ''}>6</option>
                                <option value="7" ${data.qzbl == 7 ? 'selected' : ''}>7</option>
                                <option value="8" ${data.qzbl == 8 ? 'selected' : ''}>8</option>
                                <option value="9" ${data.qzbl == 9 ? 'selected' : ''}>9</option>
                                <option value="10" ${data.qzbl == 10 ? 'selected' : ''}>10</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">广告ID</label>
                        <div class="layui-input-block">
                            <textarea name="ggid" 
                                      placeholder="请输入广告ID" class="layui-textarea">${data.ggid || ''}</textarea>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="configForm">确定</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </form>
            `;
            
            layer.open({
                type: 1,
                title: title,
                area: ['500px', '550px'],
                content: content,
                success: function(layero, index) {
                    form.render();
                    
                    // 监听提交
                    form.on('submit(configForm)', function(formData){
                        layer.load();
                        
                        fetch('system_config.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: new URLSearchParams(formData.field)
                        })
                        .then(response => response.json())
                        .then(result => {
                            layer.closeAll('loading');
                            if(result.code === 0) {
                                layer.msg(result.msg, {icon: 1});
                                layer.close(index);
                                table.reload('configTable');
                            } else {
                                layer.msg(result.msg, {icon: 2});
                            }
                        })
                        .catch(error => {
                            layer.closeAll('loading');
                            layer.msg('请求失败: ' + error.message, {icon: 2});
                        });
                        
                        return false;
                    });
                }
            });
        }
        
        // 刷新表格
        window.refreshTable = function() {
            table.reload('configTable');
        }
    });
    </script>
</body>
</html>












