<?php
/**
 * 易支付同步返回处理
 */

// 引入易支付配置
$config = include 'epay_config.php';

/**
 * 验证签名
 */
function verifySign($params, $key) {
    // 过滤空值和签名参数
    $params = array_filter($params, function($value, $k) {
        return $value !== '' && $value !== null && $k != 'sign' && $k != 'sign_type';
    }, ARRAY_FILTER_USE_BOTH);

    // 按照参数名ASCII码从小到大排序
    ksort($params);

    // 拼接成URL键值对
    $stringA = '';
    foreach ($params as $k => $v) {
        $stringA .= "{$k}={$v}&";
    }
    $stringA = rtrim($stringA, '&');

    // 拼接商户密钥并进行MD5加密
    $stringSignTemp = $stringA . $key;
    return md5($stringSignTemp);
}

// 获取返回参数
$params = $_GET + $_POST;

// 记录调试信息
error_log("epay_return.php 收到参数: " . json_encode($params));

// 验证必要参数（移除name参数，因为URL中没有）
$requiredParams = ['pid', 'trade_no', 'out_trade_no', 'type', 'money', 'trade_status', 'sign'];
foreach ($requiredParams as $param) {
    if (!isset($params[$param])) {
        $error = '参数不完整: 缺少 ' . $param;
        error_log("epay_return.php 参数验证失败: 缺少 " . $param);
        break;
    }
}

// 验证商户ID
if (!isset($error) && $params['pid'] != $config['pid']) {
    $error = '商户ID不匹配';
}

// 验证签名
if (!isset($error)) {
    $sign = verifySign($params, $config['key']);
    if ($sign !== $params['sign']) {
        $error = '签名验证失败';
    }
}

// 验证支付状态
$paymentSuccess = false;
if (!isset($error) && $params['trade_status'] === 'TRADE_SUCCESS') {
    $paymentSuccess = true;

    // 如果支付成功，直接在这里处理会员开通（备用方案）
    try {
        // 连接数据库
        $servername = "localhost";
        $username = "root";
        $password = "123456";
        $dbname = "fbsbs";

        $conn = new mysqli($servername, $username, $password, $dbname);
        $conn->set_charset("utf8");

        if (!$conn->connect_error) {
            // 解析业务参数
            $paramData = json_decode($params['param'] ?? '{}', true);
            $user = $paramData['user'] ?? '';
            $package = $paramData['package'] ?? '';

            if (!empty($user) && !empty($package)) {
                // 检查用户是否存在
                $userStmt = $conn->prepare("SELECT * FROM user WHERE user = ?");
                $userStmt->bind_param("s", $user);
                $userStmt->execute();
                $userResult = $userStmt->get_result();

                if ($userResult->num_rows > 0) {
                    $userInfo = $userResult->fetch_assoc();
                    $currentVip = $userInfo['vip'];

                    // 计算新的VIP到期日期
                    $currentDate = date('Y-m-d');
                    $startDate = ($currentVip > $currentDate) ? $currentVip : $currentDate;

                    switch ($package) {
                        case '1': // 月套餐
                            $newVipDate = date('Y-m-d', strtotime($startDate . ' +1 month'));
                            break;
                        case '3': // 季套餐
                            $newVipDate = date('Y-m-d', strtotime($startDate . ' +3 months'));
                            break;
                        case 'year': // 年套餐
                            $newVipDate = date('Y-m-d', strtotime($startDate . ' +1 year'));
                            break;
                        case 'forever': // 永久套餐
                            $newVipDate = '2099-12-31';
                            break;
                        default:
                            $newVipDate = null;
                    }

                    if ($newVipDate) {
                        // 检查是否已经处理过这个订单
                        $checkStmt = $conn->prepare("SELECT * FROM payment_log WHERE out_trade_no = ? AND status = 1");
                        $checkStmt->bind_param("s", $params['out_trade_no']);
                        $checkStmt->execute();
                        $checkResult = $checkStmt->get_result();

                        if ($checkResult->num_rows === 0) {
                            // 开始事务
                            $conn->begin_transaction();

                            try {
                                // 更新用户VIP信息
                                $updateStmt = $conn->prepare("UPDATE user SET vip = ?, code = 1 WHERE user = ?");
                                $updateStmt->bind_param("ss", $newVipDate, $user);
                                $updateStmt->execute();

                                // 记录支付日志
                                $logStmt = $conn->prepare("INSERT INTO payment_log (user, package_type, price, vip_date, payment_time, out_trade_no, trade_no, status) VALUES (?, ?, ?, ?, NOW(), ?, ?, 1)");
                                $logStmt->bind_param("ssssss", $user, $package, $params['money'], $newVipDate, $params['out_trade_no'], $params['trade_no']);
                                $logStmt->execute();

                                // 提交事务
                                $conn->commit();

                                // 记录成功日志
                                error_log("epay_return.php 成功开通会员: user=" . $user . ", vip=" . $newVipDate);

                            } catch (Exception $e) {
                                $conn->rollback();
                                error_log("epay_return.php 开通会员失败: " . $e->getMessage());
                            }
                        }
                    }
                }
            }
        }

        $conn->close();
    } catch (Exception $e) {
        error_log("epay_return.php 处理异常: " . $e->getMessage());
    }
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付结果</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 500px;
            margin: 50px auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success-icon {
            color: #4CAF50;
            font-size: 60px;
            margin-bottom: 20px;
        }
        .error-icon {
            color: #f44336;
            font-size: 60px;
            margin-bottom: 20px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .message {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        .btn {
            background: #5757ff;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #4545cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <?php if ($paymentSuccess): ?>
            <div class="success-icon">✓</div>
            <div class="title" style="color: #4CAF50;">支付成功</div>
            <div class="message">
                恭喜您！会员服务已成功开通<br>
                订单号：<?php echo htmlspecialchars($params['out_trade_no']); ?><br>
                支付金额：￥<?php echo htmlspecialchars($params['money']); ?>
            </div>
        <?php else: ?>
            <div class="error-icon">✗</div>
            <div class="title" style="color: #f44336;">支付失败</div>
            <div class="message">
                <?php echo isset($error) ? htmlspecialchars($error) : '支付未完成或已取消'; ?>
            </div>
        <?php endif; ?>
        
        <a href="/" class="btn">返回首页</a>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
    <script>
        // 3秒后自动跳转
        setTimeout(function() {
            window.location.href = '/';
        }, 3000);
        
        <?php if ($paymentSuccess): ?>
        // 如果支付成功，显示提示并跳转到自动同步设置
        swal({
            title: "支付成功!",
            text: "会员服务已开通，即将跳转到设置页面",
            type: "success",
            timer: 2000,
            showConfirmButton: false
        }, function() {
            window.location.href = '/?show_auto_sync=1';
        });
        <?php endif; ?>
    </script>
</body>
</html>
