# 易支付集成配置说明

## 🔧 配置步骤

### 1. 修改易支付配置文件
编辑 `epay_config.php` 文件，填入您的真实信息：

```php
return [
    // 商户配置 - 请替换为您的真实信息
    'pid' => '您的商户ID',  
    'key' => '您的商户密钥',  
    
    // 通知地址 - 请替换为您的真实域名
    'notify_url' => 'http://localhost/epay_notify.php',  
    'return_url' => 'http://localhost/epay_return.php',  
    
    // 网站信息
    'sitename' => 'Zeep步数同步',
];
```

### 2. 数据库更新
执行以下SQL更新支付日志表结构：

```sql
-- 如果已存在 payment_log 表，先删除
DROP TABLE IF EXISTS `payment_log`;

-- 创建新的支付日志表
CREATE TABLE `payment_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `package_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `price` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `vip_date` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `payment_time` datetime NOT NULL,
  `out_trade_no` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `trade_no` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `status` tinyint(1) DEFAULT 0 COMMENT '0:未支付 1:已支付',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_index`(`user`) USING BTREE,
  INDEX `out_trade_no_index`(`out_trade_no`) USING BTREE,
  INDEX `trade_no_index`(`trade_no`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;
```

### 3. 文件权限设置
确保以下文件有正确的权限：
- `epay_notify.php` - 需要可执行权限
- `epay_return.php` - 需要可执行权限
- `EpayHelper.php` - 需要可读权限

## 📋 支付流程说明

### 用户支付流程：
1. 用户选择套餐并点击"立即购买"
2. 系统调用易支付接口创建订单
3. 根据返回结果：
   - **PC端**：跳转到支付页面
   - **移动端**：显示二维码扫码支付
   - **微信内**：调用微信支付
4. 用户完成支付
5. 易支付发送异步通知到 `epay_notify.php`
6. 系统更新用户VIP状态
7. 用户跳转到成功页面

### 异步通知处理：
- `epay_notify.php` 处理支付成功通知
- 验证签名和支付状态
- 更新用户VIP信息
- 记录支付日志

### 同步返回处理：
- `epay_return.php` 处理用户支付完成后的跳转
- 显示支付结果页面
- 自动跳转到首页

## 🔍 测试步骤

### 1. 配置测试
1. 修改 `epay_config.php` 中的配置信息
2. 确保通知URL可以正常访问
3. 测试数据库连接

### 2. 支付测试
1. 选择套餐进行支付
2. 检查是否正确跳转到支付页面
3. 完成支付后检查VIP状态是否更新

### 3. 通知测试
1. 检查 `epay_notify.php` 是否能正常接收通知
2. 验证支付日志是否正确记录
3. 确认用户VIP日期是否正确计算

## ⚠️ 注意事项

1. **域名配置**：确保 `notify_url` 和 `return_url` 使用正确的域名
2. **HTTPS**：生产环境建议使用HTTPS
3. **签名验证**：所有通知都会验证签名，确保安全性
4. **重复通知**：系统会检查订单是否已处理，避免重复处理
5. **错误处理**：支付失败时会显示具体错误信息

## 🛠️ 故障排除

### 支付失败常见原因：
1. 商户ID或密钥错误
2. 签名验证失败
3. 通知URL无法访问
4. 数据库连接失败

### 调试方法：
1. 检查 `process_payment.php` 的响应
2. 查看 `epay_notify.php` 的日志
3. 验证易支付接口返回的错误信息

## 📞 技术支持

如果遇到问题，请检查：
1. 易支付商户后台配置
2. 服务器错误日志
3. 数据库连接状态
4. 文件权限设置
