<?php
require_once 'config/config.php';

$username = 'admin';
$password = 'admin123';
$email = '<EMAIL>';

try {
    $db = getDB();
    
    // 先删除现有账号
    $stmt = $db->prepare("DELETE FROM admin_users WHERE username = ?");
    $stmt->execute([$username]);
    
    // 创建新账号
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    $stmt = $db->prepare("INSERT INTO admin_users (username, password, email) VALUES (?, ?, ?)");
    $stmt->execute([$username, $hashedPassword, $email]);
    
    echo "账号创建成功！\n";
    echo "用户名: " . $username . "\n";
    echo "密码: " . $password . "\n";
    
} catch (Exception $e) {
    echo "创建失败: " . $e->getMessage();
}
?>