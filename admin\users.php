<?php
session_start();
require_once '../config/config.php';
require_once 'auth.php';

// 验证登录状态
checkAdminLogin();

// 处理删除用户
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['user'])) {
    header('Content-Type: application/json');
    try {
        $db = getDB();
        $stmt = $db->prepare("DELETE FROM user WHERE user = ?");
        $stmt->execute([$_GET['user']]);
        echo json_encode(['code' => 0, 'msg' => '删除成功']);
        exit;
    } catch (Exception $e) {
        echo json_encode(['code' => 1, 'msg' => '删除失败: ' . $e->getMessage()]);
        exit;
    }
}

// 处理添加/编辑用户
if ($_POST) {
    header('Content-Type: application/json');
    $action = $_POST['action'];
    $user = $_POST['user'];
    $pass = $_POST['pass'];
    $vip = $_POST['vip'] ?? '';
    $gnvip = $_POST['gnvip'] ?? '';
    $code = $_POST['code'] ?? '';
    $bs = $_POST['bs'] ?? '';
    $xcx = $_POST['xcx'] ?? '';
    
    try {
        $db = getDB();
        
        if ($action == 'add') {
            $stmt = $db->prepare("INSERT INTO user (user, pass, vip, gnvip, code, bs, xcx) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$user, $pass, $vip, $gnvip, $code, $bs, $xcx]);
            echo json_encode(['code' => 0, 'msg' => '添加成功']);
        } else if ($action == 'edit') {
            $oldUser = $_POST['old_user'];
            $stmt = $db->prepare("UPDATE user SET user=?, pass=?, vip=?, gnvip=?, code=?, bs=?, xcx=? WHERE user=?");
            $stmt->execute([$user, $pass, $vip, $gnvip, $code, $bs, $xcx, $oldUser]);
            echo json_encode(['code' => 0, 'msg' => '修改成功']);
        }
    } catch (Exception $e) {
        echo json_encode(['code' => 1, 'msg' => '操作失败: ' . $e->getMessage()]);
    }
    exit;
}

// 获取用户列表
if (isset($_GET['json'])) {
    header('Content-Type: application/json');
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $offset = ($page - 1) * $limit;
    
    // 获取搜索参数
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';

    try {
        $db = getDB();
        
        // 构建查询条件
        $whereClause = '';
        $params = [];
        if (!empty($search)) {
            $whereClause = " WHERE user LIKE ?";
            $params[] = "%$search%";
        }
        
        // 获取总数
        $countSql = "SELECT COUNT(*) as total FROM user" . $whereClause;
        if (!empty($params)) {
            $stmt = $db->prepare($countSql);
            $stmt->execute($params);
        } else {
            $stmt = $db->query($countSql);
        }
        $total = $stmt->fetch()['total'];
        
        // 获取用户列表
        $sql = "SELECT user, pass, vip, gnvip, code, bs, xcx FROM user" . $whereClause . " LIMIT $offset, $limit";
        if (!empty($params)) {
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
        } else {
            $stmt = $db->query($sql);
        }
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'code' => 0,
            'msg' => '',
            'count' => $total,
            'data' => $users
        ]);
        exit;
    } catch (Exception $e) {
        echo json_encode([
            'code' => 1,
            'msg' => '获取数据失败: ' . $e->getMessage(),
            'count' => 0,
            'data' => []
        ]);
        exit;
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>用户管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://unpkg.com/layui@2.8.18/dist/css/layui.css" rel="stylesheet">
</head>
<body>
    <div style="padding: 15px;">
        <div class="layui-card">
            <div class="layui-card-header">
                <h3>用户管理</h3>
                <div class="layui-btn-group">
                    <button class="layui-btn layui-btn-sm" onclick="addUser()">
                        <i class="layui-icon layui-icon-add-1"></i> 添加用户
                    </button>
                    <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="refreshTable()">
                        <i class="layui-icon layui-icon-refresh"></i> 刷新
                    </button>
                </div>
            </div>
            <div class="layui-card-body">
                <!-- 搜索区域 -->
                <div class="layui-form layui-form-pane" style="margin-bottom: 15px;">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">用户名</label>
                            <div class="layui-input-inline">
                                <input type="text" id="searchUser" placeholder="请输入用户名" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn" onclick="searchUsers()">
                                <i class="layui-icon layui-icon-search"></i> 搜索
                            </button>
                            <button class="layui-btn layui-btn-primary" onclick="resetSearch()">
                                <i class="layui-icon layui-icon-refresh"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>
                
                <table class="layui-hide" id="userTable" lay-filter="userTable"></table>
            </div>
        </div>
    </div>

    <!-- 操作按钮模板 -->
    <script type="text/html" id="barTpl">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
    </script>

    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script>
    layui.use(['table', 'layer', 'form'], function(){
        var table = layui.table;
        var layer = layui.layer;
        var form = layui.form;
        
        // 渲染表格
        table.render({
            elem: '#userTable',
            url: 'users.php?json=1',
            page: true,
            cols: [[
                {field: 'user', title: '用户名', width: 120},
                {field: 'pass', title: '密码', width: 120},
                {field: 'vip', title: '会员VIP', width: 100},
                {field: 'gnvip', title: '功能VIP', width: 100},
                {field: 'code', title: '代码', width: 120},
                {field: 'bs', title: '步数', width: 100},
                {field: 'xcx', title: '小程序', width: 100},
                {title: '操作', toolbar: '#barTpl', width: 150}
            ]],
            response: {
                statusName: 'code',
                statusCode: 0,
                msgName: 'msg',
                countName: 'count',
                dataName: 'data'
            }
        });
        
        // 搜索用户
        window.searchUsers = function() {
            var searchValue = document.getElementById('searchUser').value.trim();
            table.reload('userTable', {
                where: {
                    search: searchValue
                },
                page: {
                    curr: 1
                }
            });
        }
        
        // 重置搜索
        window.resetSearch = function() {
            document.getElementById('searchUser').value = '';
            table.reload('userTable', {
                where: {},
                page: {
                    curr: 1
                }
            });
        }
        
        // 回车搜索
        document.getElementById('searchUser').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchUsers();
            }
        });
        
        // 监听工具条
        table.on('tool(userTable)', function(obj){
            var data = obj.data;
            if(obj.event === 'del'){
                layer.confirm('确定删除这个用户吗？', function(index){
                    layer.close(index);
                    
                    layer.load();
                    fetch('users.php?action=delete&user=' + encodeURIComponent(data.user))
                    .then(response => response.json())
                    .then(result => {
                        layer.closeAll('loading');
                        if(result.code === 0) {
                            layer.msg('删除成功', {icon: 1});
                            obj.del();
                        } else {
                            layer.msg(result.msg, {icon: 2});
                        }
                    })
                    .catch(error => {
                        layer.closeAll('loading');
                        layer.msg('请求失败: ' + error.message, {icon: 2});
                    });
                });
            } else if(obj.event === 'edit'){
                editUser(data);
            }
        });
        
        // 添加用户
        window.addUser = function() {
            showUserForm('add', {});
        }
        
        // 编辑用户
        function editUser(data) {
            showUserForm('edit', data);
        }
        
        // 显示用户表单
        function showUserForm(action, data) {
            var title = action === 'add' ? '添加用户' : '编辑用户';
            var content = `
                <form class="layui-form" style="padding: 20px;">
                    <input type="hidden" name="action" value="${action}">
                    ${action === 'edit' ? `<input type="hidden" name="old_user" value="${data.user || ''}">` : ''}
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">用户名</label>
                        <div class="layui-input-block">
                            <input type="text" name="user" required lay-verify="required" 
                                   placeholder="请输入用户名" class="layui-input" value="${data.user || ''}">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">密码</label>
                        <div class="layui-input-block">
                            <input type="text" name="pass" required lay-verify="required" 
                                   placeholder="请输入密码" class="layui-input" value="${data.pass || ''}">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">VIP</label>
                        <div class="layui-input-block">
                            <input type="text" name="vip" placeholder="VIP状态" class="layui-input" value="${data.vip || ''}">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">功能VIP</label>
                        <div class="layui-input-block">
                            <input type="text" name="gnvip" placeholder="功能VIP" class="layui-input" value="${data.gnvip || ''}">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">代码</label>
                        <div class="layui-input-block">
                            <input type="text" name="code" placeholder="代码" class="layui-input" value="${data.code || ''}">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">步数</label>
                        <div class="layui-input-block">
                            <input type="text" name="bs" placeholder="步数" class="layui-input" value="${data.bs || ''}">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">小程序</label>
                        <div class="layui-input-block">
                            <input type="text" name="xcx" placeholder="小程序" class="layui-input" value="${data.xcx || ''}">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="userForm">确定</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </form>
            `;
            
            layer.open({
                type: 1,
                title: title,
                area: ['500px', '600px'],
                content: content,
                success: function(layero, index) {
                    form.render();
                    
                    // 监听提交
                    form.on('submit(userForm)', function(formData){
                        layer.load();
                        
                        fetch('users.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: new URLSearchParams(formData.field)
                        })
                        .then(response => response.json())
                        .then(result => {
                            layer.closeAll('loading');
                            if(result.code === 0) {
                                layer.msg(result.msg, {icon: 1});
                                layer.close(index);
                                table.reload('userTable');
                            } else {
                                layer.msg(result.msg, {icon: 2});
                            }
                        })
                        .catch(error => {
                            layer.closeAll('loading');
                            layer.msg('请求失败: ' + error.message, {icon: 2});
                        });
                        
                        return false;
                    });
                }
            });
        }
        
        // 刷新表格
        window.refreshTable = function() {
            table.reload('userTable');
        }
    });
    </script>
</body>
</html>








