<?php
session_start();
require_once '../config/config.php';
require_once 'auth.php';

// 验证登录状态
checkAdminLogin();

$admin = getCurrentAdmin();
$message = '';
$messageType = '';

if ($_POST) {
    $oldPassword = $_POST['old_password'];
    $newPassword = $_POST['new_password'];
    $confirmPassword = $_POST['confirm_password'];
    
    if (empty($oldPassword) || empty($newPassword) || empty($confirmPassword)) {
        $message = '所有字段都不能为空';
        $messageType = 'error';
    } elseif ($newPassword !== $confirmPassword) {
        $message = '新密码和确认密码不一致';
        $messageType = 'error';
    } elseif (strlen($newPassword) < 6) {
        $message = '新密码长度不能少于6位';
        $messageType = 'error';
    } else {
        try {
            $db = getDB();
            
            // 验证原密码
            $stmt = $db->prepare("SELECT password FROM admin_users WHERE id = ?");
            $stmt->execute([$admin['id']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && password_verify($oldPassword, $user['password'])) {
                // 更新密码
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                $stmt = $db->prepare("UPDATE admin_users SET password = ? WHERE id = ?");
                $stmt->execute([$hashedPassword, $admin['id']]);
                
                $message = '密码修改成功！';
                $messageType = 'success';
            } else {
                $message = '原密码错误';
                $messageType = 'error';
            }
        } catch (Exception $e) {
            $message = '修改失败，请稍后重试';
            $messageType = 'error';
        }
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>修改密码</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://unpkg.com/layui@2.8.18/dist/css/layui.css" rel="stylesheet">
</head>
<body>
    <div style="padding: 20px;">
        <div class="layui-card">
            <div class="layui-card-header">
                <h3>修改密码</h3>
            </div>
            <div class="layui-card-body">
                <?php if ($message): ?>
                    <div class="layui-elem-quote layui-quote-nm <?php echo $messageType == 'success' ? 'layui-bg-green' : 'layui-bg-red'; ?>" style="color: white;">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>
                
                <form class="layui-form" method="POST" style="max-width: 500px;">
                    <div class="layui-form-item">
                        <label class="layui-form-label">当前用户</label>
                        <div class="layui-input-block">
                            <input type="text" value="<?php echo htmlspecialchars($admin['username']); ?>" 
                                   readonly class="layui-input layui-disabled">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">原密码</label>
                        <div class="layui-input-block">
                            <input type="password" name="old_password" required lay-verify="required" 
                                   placeholder="请输入原密码" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">新密码</label>
                        <div class="layui-input-block">
                            <input type="password" name="new_password" required lay-verify="required" 
                                   placeholder="请输入新密码(至少6位)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">确认密码</label>
                        <div class="layui-input-block">
                            <input type="password" name="confirm_password" required lay-verify="required" 
                                   placeholder="请再次输入新密码" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="formDemo">确认修改</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script>
    layui.use(['form', 'layer'], function(){
        var form = layui.form;
        var layer = layui.layer;
        
        // 监听提交
        form.on('submit(formDemo)', function(data){
            var newPassword = data.field.new_password;
            var confirmPassword = data.field.confirm_password;
            
            if (newPassword !== confirmPassword) {
                layer.msg('新密码和确认密码不一致', {icon: 2});
                return false;
            }
            
            if (newPassword.length < 6) {
                layer.msg('新密码长度不能少于6位', {icon: 2});
                return false;
            }
            
            return true;
        });
        
        <?php if ($messageType == 'success'): ?>
        layer.msg('密码修改成功！', {icon: 1});
        <?php elseif ($messageType == 'error'): ?>
        layer.msg('<?php echo $message; ?>', {icon: 2});
        <?php endif; ?>
    });
    </script>
</body>
</html>