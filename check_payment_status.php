<?php
header('Content-Type: application/json; charset=utf-8');

// 引入易支付配置
$config = include 'epay_config.php';

try {
    // 获取订单号
    $trade_no = $_GET['trade_no'] ?? '';
    $out_trade_no = $_GET['out_trade_no'] ?? '';

    if (empty($trade_no) && empty($out_trade_no)) {
        throw new Exception('缺少订单号参数');
    }

    // 构建查询URL
    $queryUrl = 'https://mpay.5xv.cn/xpay/epay/api.php?act=order&pid=' . $config['pid'] . '&key=' . $config['key'];

    if (!empty($trade_no)) {
        $queryUrl .= '&trade_no=' . urlencode($trade_no);
    } else {
        $queryUrl .= '&out_trade_no=' . urlencode($out_trade_no);
    }

    // 发送查询请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $queryUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        throw new Exception('查询请求失败: ' . $error);
    }

    if ($httpCode !== 200) {
        throw new Exception('查询请求HTTP错误: ' . $httpCode);
    }

    $result = json_decode($response, true);

    if (!$result) {
        throw new Exception('查询响应格式错误: ' . $response);
    }

    if ($result['code'] != 1) {
        throw new Exception($result['msg'] ?? '查询订单失败');
    }

    // 检查支付状态
    $orderInfo = $result;
    $isPaid = ($orderInfo['status'] == 1); // 1表示已支付

    $response = [
        'success' => true,
        'paid' => $isPaid,
        'trade_no' => $orderInfo['trade_no'] ?? '',
        'out_trade_no' => $orderInfo['out_trade_no'] ?? '',
        'money' => $orderInfo['money'] ?? '',
        'type' => $orderInfo['type'] ?? '',
        'name' => $orderInfo['name'] ?? '',
        'addtime' => $orderInfo['addtime'] ?? '',
        'endtime' => $orderInfo['endtime'] ?? '',
        'status' => $orderInfo['status'] ?? 0,
        'message' => $isPaid ? '支付成功' : '等待支付'
    ];

    // 如果支付成功，更新用户会员状态
    if ($isPaid) {
        try {
            // 连接数据库
            require_once 'config.php';
            $conn = new mysqli($config['db_host'], $config['db_user'], $config['db_pass'], $config['db_name']);

            if ($conn->connect_error) {
                error_log("数据库连接失败: " . $conn->connect_error);
            } else {
                $conn->set_charset("utf8");

                // 查询本地订单信息
                $stmt = $conn->prepare("SELECT * FROM orders WHERE trade_no = ? OR out_trade_no = ?");
                $stmt->bind_param("ss", $orderInfo['trade_no'], $orderInfo['out_trade_no']);
                $stmt->execute();
                $localOrderResult = $stmt->get_result();

                if ($localOrderResult->num_rows > 0) {
                    $localOrder = $localOrderResult->fetch_assoc();
                    $user = $localOrder['user'];
                    $package = $localOrder['package'];

                    // 更新本地订单状态
                    $updateOrderStmt = $conn->prepare("UPDATE orders SET status = 'paid' WHERE id = ?");
                    $updateOrderStmt->bind_param("i", $localOrder['id']);
                    $updateOrderStmt->execute();

                    // 获取套餐信息并更新用户会员状态
                    $packageStmt = $conn->prepare("SELECT * FROM packages WHERE name = ?");
                    $packageStmt->bind_param("s", $package);
                    $packageStmt->execute();
                    $packageResult = $packageStmt->get_result();

                    if ($packageResult->num_rows > 0) {
                        $packageInfo = $packageResult->fetch_assoc();
                        $days = $packageInfo['days'];

                        // 计算会员到期时间
                        $expireDate = date('Y-m-d H:i:s', strtotime("+{$days} days"));

                        // 更新用户会员状态
                        $updateUserStmt = $conn->prepare("UPDATE user SET vip = 1, vip_expire = ? WHERE user = ?");
                        $updateUserStmt->bind_param("ss", $expireDate, $user);
                        $updateUserStmt->execute();

                        $response['vip_updated'] = true;
                        $response['vip_expire'] = $expireDate;
                        $response['package'] = $package;

                        error_log("用户会员状态已更新: {$user}, 到期时间: {$expireDate}");
                    }
                }

                $conn->close();
            }
        } catch (Exception $e) {
            error_log("更新用户会员状态失败: " . $e->getMessage());
        }

        // 记录支付成功日志
        $logData = [
            'time' => date('Y-m-d H:i:s'),
            'action' => 'payment_success_check',
            'trade_no' => $orderInfo['trade_no'],
            'out_trade_no' => $orderInfo['out_trade_no'],
            'money' => $orderInfo['money'],
            'type' => $orderInfo['type']
        ];
        error_log("支付成功检查: " . json_encode($logData));
    }

    echo json_encode($response);

} catch (Exception $e) {
    $errorResponse = [
        'success' => false,
        'paid' => false,
        'message' => $e->getMessage(),
        'debug' => [
            'trade_no' => $trade_no ?? '',
            'out_trade_no' => $out_trade_no ?? '',
            'query_url' => $queryUrl ?? ''
        ]
    ];

    echo json_encode($errorResponse);
}
?>
