<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付页面 - <PERSON>eep步数同步</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .payment-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        
        .payment-header {
            margin-bottom: 30px;
        }
        
        .payment-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .payment-subtitle {
            color: #666;
            font-size: 14px;
        }
        
        .qr-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }
        
        .qr-code {
            width: 200px;
            height: 200px;
            margin: 0 auto 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
        }
        
        .payment-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            font-size: 14px;
        }
        
        .info-label {
            color: #666;
        }
        
        .info-value {
            color: #333;
            font-weight: 500;
        }
        
        .payment-tips {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
            text-align: left;
            border-radius: 5px;
        }
        
        .tips-title {
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 8px;
        }
        
        .tips-list {
            color: #666;
            font-size: 13px;
            line-height: 1.5;
        }
        
        .action-buttons {
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dee2e6;
        }
        
        .btn-secondary:hover {
            background: #e9ecef;
        }
        
        .countdown {
            color: #ff6b6b;
            font-weight: bold;
            margin-top: 15px;
        }
        
        @media (max-width: 480px) {
            .payment-container {
                padding: 20px;
                margin: 10px;
            }
            
            .qr-code {
                width: 180px;
                height: 180px;
            }
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <div class="payment-header">
            <div class="payment-title">扫码支付</div>
            <div class="payment-subtitle">请使用支付宝或微信扫描下方二维码</div>
        </div>
        
        <div class="qr-container">
            <img id="qrcode" class="qr-code" alt="支付二维码" />
            <div style="color: #666; font-size: 14px;">请扫描二维码完成支付</div>
        </div>
        
        <div class="payment-info">
            <div class="info-row">
                <span class="info-label">订单号：</span>
                <span class="info-value" id="tradeNo">-</span>
            </div>
            <div class="info-row">
                <span class="info-label">支付方式：</span>
                <span class="info-value" id="payType">-</span>
            </div>
            <div class="info-row">
                <span class="info-label">支付金额：</span>
                <span class="info-value" id="payAmount">-</span>
            </div>
        </div>
        
        <div class="payment-tips">
            <div class="tips-title">支付说明</div>
            <div class="tips-list">
                • 请在15分钟内完成支付，超时订单将自动取消<br>
                • 支付成功后会员权限将立即生效<br>
                • 如遇问题请联系客服处理
            </div>
        </div>
        
        <div class="countdown" id="countdown">
            订单有效期：<span id="timeLeft">15:00</span>
        </div>
        
        <div class="action-buttons">
            <button class="btn btn-primary" onclick="checkPaymentStatus()">检查支付状态</button>
            <button class="btn btn-secondary" onclick="window.close()">关闭页面</button>
        </div>
    </div>

    <script>
        // 获取URL参数
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }
        
        // 初始化页面
        function initPayment() {
            console.log('初始化支付页面...');
            console.log('当前URL:', window.location.href);

            var qrcode = getUrlParameter('qrcode');
            var tradeNo = getUrlParameter('trade_no');
            var payType = getUrlParameter('pay_type') || 'alipay';
            var amount = getUrlParameter('amount') || '未知';

            console.log('支付页面参数:', {
                qrcode: qrcode,
                tradeNo: tradeNo,
                payType: payType,
                amount: amount,
                qrcodeLength: qrcode ? qrcode.length : 0
            });

            // 设置订单信息
            document.getElementById('tradeNo').textContent = tradeNo || '未知';
            document.getElementById('payType').textContent = payType === 'alipay' ? '支付宝' : '微信支付';
            document.getElementById('payAmount').textContent = '￥' + amount;

            // 生成二维码
            if (qrcode && qrcode.trim() !== '') {
                console.log('开始生成二维码...');
                generateQRCode(qrcode);
            } else {
                console.error('二维码数据为空或无效');
                document.querySelector('.qr-container').innerHTML =
                    '<div style="padding: 20px; color: #ff6b6b;">二维码数据无效，请重新下单</div>';
            }

            // 启动倒计时
            startCountdown(15 * 60); // 15分钟
        }
        
        // 生成二维码
        function generateQRCode(data) {
            console.log('生成二维码，原始数据:', data);

            if (!data || data.trim() === '') {
                console.error('二维码数据为空');
                showPaymentLink(data);
                return;
            }

            // 检查是否是支付宝链接，如果是，直接显示链接按钮
            if (data.includes('qr.alipay.com') || data.includes('alipay.com')) {
                console.log('检测到支付宝链接，显示支付按钮');
                showAlipayButton(data);
                return;
            }

            // 尝试生成二维码
            var qrImageUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(data);
            var qrImage = document.getElementById('qrcode');

            qrImage.onload = function() {
                console.log('二维码加载成功');
            };

            qrImage.onerror = function() {
                console.error('二维码生成失败，显示支付链接');
                showPaymentLink(data);
            };

            qrImage.src = qrImageUrl;
        }

        // 显示支付宝按钮
        function showAlipayButton(alipayUrl) {
            var container = document.querySelector('.qr-container');
            container.innerHTML =
                '<div style="padding: 30px; text-align: center;">' +
                '<div style="font-size: 48px; margin-bottom: 20px;">💰</div>' +
                '<p style="color: #666; margin-bottom: 20px;">点击下方按钮打开支付宝支付</p>' +
                '<a href="' + alipayUrl + '" target="_blank" style="display: inline-block; background: #1677ff; color: white; padding: 15px 30px; border-radius: 8px; text-decoration: none; font-size: 16px; font-weight: bold;">打开支付宝支付</a>' +
                '<p style="color: #999; font-size: 12px; margin-top: 15px;">如果按钮无效，请复制下方链接到浏览器打开</p>' +
                '<div style="background: #f5f5f5; padding: 10px; border-radius: 5px; margin-top: 10px; word-break: break-all; font-size: 12px;">' + alipayUrl + '</div>' +
                '</div>';
        }

        // 显示支付链接
        function showPaymentLink(paymentUrl) {
            var container = document.querySelector('.qr-container');
            container.innerHTML =
                '<div style="padding: 20px; background: #f8f9fa; border-radius: 10px;">' +
                '<p style="color: #666; margin-bottom: 15px;">请点击下方链接完成支付：</p>' +
                '<a href="' + paymentUrl + '" target="_blank" style="color: #667eea; word-break: break-all; font-weight: bold;">' + paymentUrl + '</a>' +
                '</div>';
        }
        
        // 检查支付状态
        function checkPaymentStatus() {
            var tradeNo = getUrlParameter('trade_no');
            if (!tradeNo) {
                alert('订单号不存在');
                return;
            }
            
            var xhr = new XMLHttpRequest();
            xhr.open('GET', 'check_payment_status.php?trade_no=' + encodeURIComponent(tradeNo), true);
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.success && response.paid) {
                                alert('支付成功！会员权限已开通，即将跳转到首页。');
                                window.location.href = '/';
                            } else {
                                alert('支付尚未完成，请继续扫码支付。');
                            }
                        } catch (e) {
                            alert('检查支付状态失败，请稍后重试。');
                        }
                    } else {
                        alert('网络错误，请稍后重试。');
                    }
                }
            };
            xhr.send();
        }
        
        // 倒计时
        function startCountdown(seconds) {
            var timeLeft = seconds;
            var timer = setInterval(function() {
                var minutes = Math.floor(timeLeft / 60);
                var secs = timeLeft % 60;
                document.getElementById('timeLeft').textContent = 
                    (minutes < 10 ? '0' : '') + minutes + ':' + (secs < 10 ? '0' : '') + secs;
                
                if (timeLeft <= 0) {
                    clearInterval(timer);
                    alert('订单已超时，请重新下单。');
                    window.close();
                }
                timeLeft--;
            }, 1000);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initPayment);
    </script>
</body>
</html>
