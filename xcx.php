<?php
$dbconfig=array('host' => 'localhost','port' => 3306,'user' => 'fbsbs','pwd' => 'fbsbsxcx...','dbname' => 'fbsbs');
//数据库账号密码↑
$host = $dbconfig["host"];$port = $dbconfig["port"];$user = $dbconfig["user"];$pwd = $dbconfig["pwd"];$dbname = $dbconfig["dbname"];
//数据库账号密码↑
$conn = mysqli_connect($host,$user,$pwd,$dbname,$port);
//连接数据库
if (!$conn){die("数据库连接失败");}
//连接失败就停止运行

//倘若连接成功则执行以下内容
// $data = json_decode($data,320);
// echo openssl_encrypt($arr, 'AES-128-CBC', $key, 0,$iv);



getOpenid();
function getOpenid() {
$conn=$GLOBALS['conn'];
$key ='vgs^&%^tshsgshs*';
$iv = '!#%^&*()^@#@$&&%';
$date=$_POST['date'];
$date = openssl_decrypt($date, 'AES-128-CBC', $key, 0,$iv);
$data=$_POST['data'];
$data = openssl_decrypt($data, 'AES-128-CBC', $key, 0,$iv);


//接收小程序端传来的数据
$date = json_decode($date,320);
$postdata = json_decode($data,320);
$root = $date['root'];
$hj = $date['hj'];
$code = $date['code'];
if(empty($root)){
echo '<html>
<head><title>404 Not Found</title></head>
<body>
<center><h1>404 Not Found</h1></center>
<hr><center>nginx</center>
</body>
</html>';exit;
}

//若小程序id为空 则返回以上数组↑   不成立继续执行以下内容↓

$data = mysqli_query($conn,"SELECT * FROM `syskey` where `appid`='$root'");
$num = mysqli_num_rows($data);
if($num==1){
$rs=$conn->query("SELECT * FROM syskey WHERE `appid`='$root' ");
$app = $rs->fetch_assoc();$appSecret=$app['appkey'];
$wxUrl = 'https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code';
$getUrl = sprintf($wxUrl, $root, $appSecret, $code);
$result = curl_get($getUrl);
$wxResult = json_decode($result, true);
if(empty($wxResult)){
$arr = array('code'  => 998,'text1' => "检测到当前你的小程序未授权!",'text2' => "如需授权可加官方QQ 联系",'text3' => "官方 QQ: 2682281633",'text4' => "微信小程序搜索  伏笔网络",'text5' => "即可体验当前小程序系统所有功能",'text6' => "QQ: 2682281633",'bqts'=>"",'sq'=>'如需授权请加QQ: 2682281633');
$data = json_encode($arr,320);
echo openssl_encrypt($data, 'AES-128-CBC', $key, 0,$iv);
}else{
$loginFail = array_key_exists('errcode', $wxResult);
if ($loginFail) {
$arr = array('code'  => 998,'text1' => "检测到当前你的小程序未授权!",'text2' => "如需授权可加官方Q群 联系群主",'text3' => "官方Q: 2682281633",'text4' => "微信小程序搜索  伏笔网络",'text5' => "即可体验当前小程序系统所有功能",'text6' => "QQ: 2682281633",'bqts'=>"",'sq'=>'如需授权请加QQ: 2682281633');
$data = json_encode($arr,320);
echo openssl_encrypt($data, 'AES-128-CBC', $key, 0,$iv);
}else{
//内容从这里开始..
$openid = addslashes($wxResult['openid']);
$jz=$postdata['jz'];
$sj = date("Y-m-d");
if($jz=='index'){
	$data=$conn->query("SELECT * FROM syskey WHERE `appid`='$root' ");
	$app = $data->fetch_assoc();
	
	
	
	$data=$conn->query("SELECT * FROM syskey WHERE `appid`='$root'");
	$app = $data->fetch_assoc();
	$gg=$app['ggid'];

	$data = mysqli_query($conn,"SELECT * FROM xcxuser WHERE `user`='$openid'");
	if(mysqli_num_rows($data)){
	    
		
		$data = mysqli_query($conn,"SELECT * FROM user WHERE `xcx`='$openid'");
	    if(mysqli_num_rows($data)){
	        
	        $rs=$conn->query("SELECT * FROM xcxuser WHERE `user`='$openid' ");
		    $jzmh_user = $rs->fetch_assoc();
		
		    if($jzmh_user['zt']==1){
			    $a = 1;
			}else{
			    $a = 0;
		    }
	    
	    }else{
	        
	        $a = 0;
	        
	    }
		
		$rs=$conn->query("SELECT * FROM xcxuser WHERE `user`='$openid' ");
		$jzmh_user = $rs->fetch_assoc();
			
		$data=array('code' => 1,'openid'=>$openid,'gg'=>$gg,'xz'=>$a,'bl'=>$app['qzbl'],'kg'=>$app['qzkg']);
		
	}else{
		
		if(mysqli_query($conn,"INSERT INTO `xcxuser` (`user`, `zt`) VALUES ('$openid','0')")){
		    
// 			$rs=$conn->query("SELECT * FROM xcxuser WHERE `user`='$openid' ");
			
// 			$jzmh_user = $rs->fetch_assoc();
			
// 			$jzmh_user
			
			$data=array('code' => 1,'openid'=>$openid,'gg'=>$gg,'xz'=>0,'bl'=>$app['qzbl'],'kg'=>$app['qzkg']);
		}else{
			$data=array('code'=>0);
		}
		
	}
	

	
	echo openssl_encrypt(json_encode($data,320), 'AES-128-CBC', $key, 0,$iv);
	
//注册147-155
}else if($jz=='gxgn'){
	
	if($openid==$postdata['user']){
	$data = mysqli_query($conn,"SELECT * FROM user WHERE `xcx`='$openid'");
	if(mysqli_num_rows($data)){
		
		$vip = date('Y-m-d H:i:s', strtotime('+5minute'));
		
		$tj="update `user` set `gnvip` ='{$vip}'  where `xcx`='{$openid}'";
		if($conn->query($tj)){
			$data = array('code'=>1,'msg'=>'领取成功!');
		}else{
			$data = array('code'=>0,'msg'=>'领取失败!');
		}
		
	}else{
	$data=array('code'=>1,'msg'=>'1');
	}
	}else{
	$data = array('code'=>0,'msg'=>'请勿篡改数据 小鳖3');
	}
	echo openssl_encrypt(json_encode($data,320), 'AES-128-CBC', $key, 0,$iv);
	
//注册155-148
}




//内容从这里结束..
}
}
}else{
$arr = array('code'  => 998,'text1' => "检测到当前你的小程序未授权!",'text2' => "如需授权可加官方Q 联系",'text3' => "官方Q: 2682281633",'text4' => "微信小程序搜索  伏笔网络",'text5' => "即可体验当前小程序系统所有功能",'text6' => "QQ: 2682281633",'bqts'=>"",'sq'=>'如需授权请加QQ: 2682281633');
$data = json_encode($arr,320);
echo openssl_encrypt($data, 'AES-128-CBC', $key, 0,$iv);
}
//这条是配合上面$num判断root是否存在 
}
	
	
	
	
	
	function curl_get($url, &$httpCode = 0) {
	    $ch = curl_init();
	    curl_setopt($ch, CURLOPT_URL, $url);
	    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	    //不做证书校验,部署在linux环境下请改为true
	    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
	    $file_contents = curl_exec($ch);
	    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
	    curl_close($ch);
	    return $file_contents;
	}
	
	
	
	function getData ($url){
	        $ch = curl_init();
	        $timeout = 300;
	        curl_setopt($ch,CURLOPT_URL,$url);
	        curl_setopt($ch,CURLOPT_RETURNTRANSFER,1);
	        curl_setopt($ch,CURLOPT_HEADER,0);
	        $output = curl_exec($ch);
	        curl_close($ch);
	        return $output;
	}
	
	
	function posthaed($url,$json){
			$useragent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36';
				$header = array (
				'Content-Type'=>'application/octet-stream'
				);
	    $timeout= 60;
	    $ch = curl_init();
	    curl_setopt($ch, CURLOPT_URL, $url);
	    curl_setopt($ch, CURLOPT_POST, true);
	    curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
	    curl_setopt($ch, CURLOPT_FAILONERROR, true);
	    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
	    //不取得返回头信息	
	    curl_setopt($ch, CURLOPT_HEADER, 0);
	    // 关闭https验证
	    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
	    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true );
	    curl_setopt($ch, CURLOPT_ENCODING, "" );
	    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true );
	    curl_setopt($ch, CURLOPT_AUTOREFERER, true );
	    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout );
	    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout );
	    curl_setopt($ch, CURLOPT_MAXREDIRS, 10 );
	    curl_setopt($ch, CURLOPT_USERAGENT, $useragent);
	    $content = curl_exec($ch);
	    curl_close($ch);
	    return $content;
	}
	
	
	    function http_request($url, $data = null)
	    {
	        $curl = curl_init();
	        curl_setopt($curl, CURLOPT_URL, $url);
	        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
	        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
	        if (!empty($data)) {
	            curl_setopt($curl, CURLOPT_POST, TRUE);
	            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
	        }
	        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
	        $output = curl_exec($curl);
	        curl_close($curl);
	        return $output;
	    }

	
	function jq($input, $start, $end)
	{
	    $substr = substr($input, strlen($start) + strpos($input, $start), (strlen($input) - strpos($input, $end)) * (-1));
	    return $substr;
	}
	
	
	function jk($root,$jlbf,$jldj,$qd)
	{
	    $conn=$GLOBALS['conn'];
	    $sj = date("Y-m-d");
	    $data = mysqli_query($conn,"SELECT * FROM `jzmh_jksj` where `root`='$root' and `time`='$sj'");
	    $num = mysqli_num_rows($data);
	    if($num==1)
	    {
			$data = mysqli_query($conn,"SELECT * FROM `jzmh_jksj` where `root`='$root' order by id desc");
			$ggcx = $data->fetch_assoc();
			$jlbf = $ggcx['jlbf']+$jlbf;
			$jldj = $ggcx['jldj']+$jldj;
			$qd = $ggcx['qd']+$qd;
			if(mysqli_query($conn,"update `jzmh_jksj` set `jlbf`='{$jlbf}',`jldj`='{$jldj}',`qd`='{$qd}' where `root`='{$root}' && `time`='$sj'")){
			}else{
			}
	    }else{
	      if(mysqli_query($conn,"INSERT INTO `jzmh_jksj` (`root`, `jlbf`, `jldj` , `time`,`qd`) VALUES ('$root','$jlbf', '$jldj','$sj', '$qd')")){
		  }else{
		  }
	    }

	}
	
	
		function get($url,$header){
	    $useragent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
	    $timeout= 60;
	    $ch = curl_init();
	    curl_setopt($ch, CURLOPT_URL, $url);
	    curl_setopt($ch, CURLOPT_FAILONERROR, true);
	    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
	    //不取得返回头信息	
	    curl_setopt($ch, CURLOPT_HEADER, 0);
	    // 关闭https验证
	    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
	    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true );
	    curl_setopt($ch, CURLOPT_ENCODING, "" );
	    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true );
	    curl_setopt($ch, CURLOPT_AUTOREFERER, true );
	    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout );
	    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout );
	    curl_setopt($ch, CURLOPT_MAXREDIRS, 10 );
	    curl_setopt($ch, CURLOPT_USERAGENT, $useragent);
	    $content = curl_exec($ch);
	    curl_close($ch);
	    return $content;
	}
	
	
	function gethead($url,$header){
	    $useragent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
	    $timeout= 60;
	    $ch = curl_init();
	    curl_setopt($ch, CURLOPT_URL, $url);
	    curl_setopt($ch, CURLOPT_FAILONERROR, true);
	    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
	    //不取得返回头信息	
	    curl_setopt($ch, CURLOPT_HEADER, 1);
	    // 关闭https验证
	    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
	    curl_setopt($ch, CURLOPT_ENCODING, "" );
	    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1 );
	    curl_setopt($ch, CURLOPT_AUTOREFERER, true );
	    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout );
	    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout );
	    curl_setopt($ch, CURLOPT_MAXREDIRS, 10 );
	    curl_setopt($ch, CURLOPT_USERAGENT, $useragent);
	    $content = curl_exec($ch);
	    curl_close($ch);
		preg_match_all('|Set-Cookie: (.*);|U', $content, $arr);
		$cookies = implode(';', $arr[1]);
	    return $cookies;
	}
	
	
	


?>