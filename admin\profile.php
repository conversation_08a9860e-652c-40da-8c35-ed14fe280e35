<?php
session_start();
require_once '../config/config.php';
require_once 'auth.php';

// 验证登录状态
checkAdminLogin();

$admin = getCurrentAdmin();

try {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM admin_users WHERE id = ?");
    $stmt->execute([$admin['id']]);
    $adminInfo = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $adminInfo = null;
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>个人资料</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://unpkg.com/layui@2.8.18/dist/css/layui.css" rel="stylesheet">
</head>
<body>
    <div style="padding: 20px;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h3>基本信息</h3>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-table">
                            <tbody>
                                <tr>
                                    <td>用户名</td>
                                    <td><?php echo htmlspecialchars($adminInfo['username']); ?></td>
                                </tr>
                                <tr>
                                    <td>邮箱</td>
                                    <td><?php echo htmlspecialchars($adminInfo['email']); ?></td>
                                </tr>
                                <tr>
                                    <td>创建时间</td>
                                    <td><?php echo $adminInfo['created_at']; ?></td>
                                </tr>
                                <tr>
                                    <td>最后更新</td>
                                    <td><?php echo $adminInfo['updated_at']; ?></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h3>安全设置</h3>
                    </div>
                    <div class="layui-card-body">
                        <div class="layui-form">
                            <div class="layui-form-item">
                                <button class="layui-btn layui-btn-normal" onclick="changePassword()">
                                    <i class="layui-icon layui-icon-password"></i> 修改密码
                                </button>
                            </div>
                            <div class="layui-form-item">
                                <button class="layui-btn layui-btn-warm" onclick="editProfile()">
                                    <i class="layui-icon layui-icon-edit"></i> 编辑资料
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script>
    layui.use(['layer'], function(){
        var layer = layui.layer;
        
        window.changePassword = function() {
            layer.open({
                type: 2,
                title: '修改密码',
                area: ['600px', '500px'],
                content: 'change_password.php'
            });
        }
        
        window.editProfile = function() {
            layer.msg('功能开发中...', {icon: 0});
        }
    });
    </script>
</body>
</html>