<?php
$dbconfig=array('host' => 'localhost','port' => 3306,'user' => 'fbsbs','pwd' => 'fbsbsxcx...','dbname' => 'fbsbs');
//数据库账号密码↑
$host = $dbconfig["host"];$port = $dbconfig["port"];$user = $dbconfig["user"];$pwd = $dbconfig["pwd"];$dbname = $dbconfig["dbname"];
//数据库账号密码↑
$conn = mysqli_connect($host,$user,$pwd,$dbname,$port);
//连接数据库
if (!$conn){die("数据库连接失败");}

$jz =  $_GET['jz'];
$sj =  $_POST['sj'];
$id =  $_POST['id'];
$kg =  $_POST['kg'];

if($jz=='vip'){
	
	
	
	if($id!=''&&$sj!=''){
	    
	    if($sj==1){
	        $vipsk = date('Y-m-d',strtotime("$d +3 day"));
	    }else if($sj==2){
	        $vipsk = date('Y-m-d',strtotime("$d +7 day"));
	    }else if($sj==3){
	        $vipsk = date('Y-m-d',strtotime("$d +30 day"));
	    }else if($sj==4){
	        $vipsk = date('Y-m-d',strtotime("$d +90 day"));
	    }else if($sj==5){
	        $vipsk = date('Y-m-d',strtotime("$d +365 day"));
	    }else if($sj==6){
	        $vipsk = '2099-12-12';
	    }
	    
	    
	
		$tj="update `user` set `vip` ='$vipsk' where `user`='$id'";
		
		
	    if($conn->query($tj)){
	        echo "<script>alert('成功!');location='wcbshy.php';</script>";
	    }else{
	        echo "<script>alert('失败!');location='wcbshy.php';</script>";
	    }
	    
		
	}else{
	        echo "<script>alert('请填写完整');location='wcbshy.php';</script>";
    }
	
	
	
	
	
}else if($jz=='ggsz'){
	
	if($id!=''&&$kg!=''){
	    
	    
	    
		$tj="update `syskey` set `qzkg` ='$kg',`qzbl` ='$id'  where `appid`='wxdd2b81cf5fb4764d'";
	    if($conn->query($tj)){
    	    echo "<script>alert('设置成功!');location='/wcbshy.php';</script>";
	    }else{
	        echo "<script>alert('设置失败!');location='/wcbshy.php';</script>";
	    }
		
	}else{
	    echo "<script>alert('请填写完整');location='wcbshy.php';</script>";
    }
    
	
}



?>



<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no" id="viewport" name="viewport">
    <title>会员设置</title>
    <!-- <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.js"></script> -->
</head>
<style>
	*{ margin:0; padding:0;}
	.div{
		    padding: 0px 18px;
			height: 100vh;
		    display: flex;align-items: center;
			flex-direction: column;
	}
	
	.input:focus {
	  outline: 1px solid slategray;
	  
	}

	.shadow {
		display: none;
	    position: fixed;
	    top: 0;
	    bottom: 0;
	    left: 0;
	    right: 0;
	    background: rgba(0,0,0,0.3);
	    z-index: 999;
	}
	.shadow-bg {
	    position: fixed;
	    top: 46%;
	    left: 50%;
	    transform: translate(-50%,-50%);
	    -webkit-transform: translate(-50%,-50%);
	    -moz-transform: translate(-50%,-50%);
	    padding: 20px;
		border-radius: 18px;
	    background: #fff;
	}
</style>

<body>
<div class="div">
	
	
	<div  id="ym1" style="max-width: 32rem;width: 100%;margin-top: 60px;text-align: center;" >
		
		<div style="margin: 0px 20px;text-align: left;">
			<div style="font-size: 20px;font-family: Arial, Helvetica, sans-serif;">会员自助激活</div>
			<div style="font-size: 15px;margin-top: 6px;font-family: Arial, Helvetica, sans-serif;">填写对方ID&nbsp;&nbsp;选择时间&nbsp;&nbsp;一键设置会员</div>
		</div>
	
	<form  action="wcbshy.php?jz=vip" method="post" style="width: 100%;max-width: 32rem;margin-top: 20%;display: flex;flex-direction: column;align-items: center;"enctype="multipart/form-data" name="edit-form">
		
			<input class="input" placeholder="填写对方id" name="id" value="" style="padding: 10px;border: 1px solid slategray;width: 80%;border-radius: 20px;text-align: center;" required/>
	
<select name="sj" style="width: 80%;padding: 8px;text-align: center;margin-top:20px"> 
<option value="1">三天</option> 
<option value="2">七天</option> 
<option value="3">一个月</option>
<option value="4">三个月</option> 
<option value="5">一年</option> 
<option value="6">永久</option>
</select> 
		
		
		
		
		
		
			<input type="submit" style="background: #000;color: #fff;border-radius: 12px;padding: 10px 0px;font-size: 13px;margin-top: 50px;width: 80%;" value="设置会员" />
		
		</form> 
		
		
		<form action="wcbshy.php?jz=ggsz" method="post"  style="width: 100%;max-width: 32rem;margin-top: 20%;display: flex;flex-direction: column;align-items: center;" enctype="multipart/form-data" name="edit-form">
		
			<input class="input" placeholder="广告开关 1开 0关" name="kg" value="" style="padding: 10px;border: 1px solid slategray;width: 80%;border-radius: 20px;text-align: center;" required/>
			
			<br>
			
			<input class="input" placeholder="强制比例 1-10  建议5" name="id" value="" style="padding: 10px;border: 1px solid slategray;width: 80%;border-radius: 20px;text-align: center;" required/>
		
			<input type="submit" style="background: red;color: #fff;border-radius: 12px;padding: 10px 0px;font-size: 13px;margin-top: 50px;width: 80%;" value="设置" />
		
		</form>
		
	<div>
		
<div>		
			

<div class="shadow" id="hqsb">
		        <div class="shadow-bg">
					<div style="text-align: center;font-size: 15px;font-weight: 700;">提示</div>
					<div id="sbts" style="text-align: center;font-size: 13px;margin-top: 18px;padding-bottom: 5px;color:red;font-weight: 700;">
						
					</div>
					
				</div>
</div>
</body>


</html>